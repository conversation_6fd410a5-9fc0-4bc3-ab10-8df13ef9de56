from odoo import models, fields, api, _
from datetime import timedelta
import logging

_logger = logging.getLogger(__name__)

class Recouvrement(models.Model):
    _name = 'recouvrement'
    _description = 'Recouvrement'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    message_main_attachment_id = fields.Many2one('ir.attachment', string='Main Attachment', copy=False)

    def _compute_delivery_status(self):
        for rec in self:
            if rec.order_id:
                picking_ids = rec.order_id.picking_ids
                if not picking_ids:
                    rec.delivery_status = 'no'
                elif all(pick.state == 'done' for pick in picking_ids):
                    rec.delivery_status = 'done'
                elif any(pick.state == 'done' for pick in picking_ids):
                    rec.delivery_status = 'partially'
                else:
                    rec.delivery_status = 'pending'
            else:
                rec.delivery_status = 'no'

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if not vals.get('name'):
                vals['name'] = self.env['ir.sequence'].next_by_code('recouvrement.sequence') or _('New')
        return super(Recouvrement, self).create(vals_list)

    name = fields.Char(string="Nom", readonly=True, copy=False, default=lambda x: _('New'))
    observation = fields.Text(string="Observation")
    montant_credit = fields.Monetary(string="Montant Crédit",related='order_id.opportunity_id.expected_revenue',store=True)
    resid_amount = fields.Float(string="Montant Restant",compute='_compute_resid_amount',store=True)
    maturity_date = fields.Date(string="Date d'échéance",compute='_compute_maturity_date',store=True)
    status = fields.Selection([('pending', 'En attente'), ('paid', 'Payé')], string="Statut", default='pending', compute='_compute_status',store=True)
    payment_ids = fields.One2many('account.payment', 'recouvrement_id', string="Paiements")
    inpaid_amount = fields.Float(string="Montant Non Payé",compute='_compute_inpaid_amount',store=True)
    partner_id = fields.Many2one('res.partner', string="Client")
    order_id = fields.Many2one('sale.order', string='Bon de Commande', domain="[('partner_id', '=', partner_id)]")
    counter_id = fields.Many2one('customer.reference', string='Compteur', related='order_id.counter_id',store=True)
    currency_id = fields.Many2one('res.currency', string='Currency', required=True, readonly=True, states={'draft': [('readonly', False)]},
                                  default=lambda self: self.env.company.currency_id.id)
    lead_id = fields.Many2one('crm.lead', string='Opportunité', related='order_id.opportunity_id',store=True)
    user_id = fields.Many2one('res.users', string='Responsable', related='lead_id.user_id')
    team_id = fields.Many2one('crm.team', string='Equipe', related='lead_id.team_id')

    @api.depends('payment_ids', 'payment_ids.payment_state')
    def _compute_maturity_date(self):
        for rec in self:
            versed_payments = rec.payment_ids.filtered(lambda p: p.payment_state != 'paid' and p.state == 'posted')
            _logger.info(f"versed_payments: {versed_payments}")
            if versed_payments:
                rec.maturity_date = min(versed_payments.mapped('maturity_date'))
            else:
                rec.maturity_date = False
            
    @api.depends('payment_ids', 'payment_ids.payment_state','montant_credit')
    def _compute_resid_amount(self):
        for rec in self:
            rec.resid_amount = rec.montant_credit - sum(rec.payment_ids.filtered(lambda p: p.payment_state == 'paid' and p.state == 'posted').mapped('amount'))

    @api.depends('resid_amount')
    def _compute_status(self):
        for rec in self:
            if rec.resid_amount > 0:
                rec.status = 'pending'
            elif rec.resid_amount == 0:
                rec.status = 'paid'

    @api.depends('payment_ids', 'payment_ids.payment_state')
    def _compute_inpaid_amount(self):
        for rec in self:
            rec.inpaid_amount = sum(rec.payment_ids.filtered(lambda p: p.maturity_date and p.maturity_date < fields.Date.today() and p.state == 'posted').mapped('amount'))

    def action_create_payment(self):
        return {
            'name': _('Create Payment'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.payment',
            'view_mode': 'form',
            'context': {
                'default_partner_id': self.partner_id.id,
                'default_recouvrement_id': self.id,
                'default_payment_type': 'inbound',
                'default_amount': self.resid_amount,
                'default_move_journal_types': ('bank', 'cash'),    
            },
            'target': 'new',
        }

    def send_notification(self):
        _logger.info("send_notification")
        target_date = fields.Date.today() + timedelta(days=2)
        _logger.info("target_date: %s" % target_date)
        recouvrement = self.env['recouvrement'].search([('maturity_date', '=', target_date)])
        _logger.info("recouvrement: %s" % recouvrement)
        for rec in recouvrement:
            _logger.info(f"rec.maturity_date: {rec.maturity_date}")
            message = 'Echéance du client %s est %s' % (rec.partner_id.name, rec.maturity_date)
            if rec.lead_id.user_id:
                rec.env['custom.notification'].create({
                'user_id': rec.lead_id.user_id.id,
                'message': message,
                'recouvrement_id': rec.id,
                'date_notification': fields.Datetime.now(),
                })

            group = self.env.ref('recouvrement.group_recouvrement_manager')
            users = group.users  # Fetch all users in the group
            for user in users:

                rec.env['custom.notification'].create({
                'user_id': user.id,
                'message': message,
                'recouvrement_id': rec.id,
                'date_notification': fields.Datetime.now(),
                })

    def action_import_payments(self):
        return {
            'name': _('Import Payments'),
            'type': 'ir.actions.act_window',
            'res_model': 'import.payments.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'active_id': self.id}
        }

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        user = self.env.user
        if user.has_group('recouvrement.group_recouvrement_manager'):
            return super(Recouvrement, self).search_read(domain, fields, offset, limit, order)
        team_ids = self.env['crm.team'].search([('member_ids', 'in', [user.id])]).ids
        team_domain = []
        if user.has_group('recouvrement.group_recouvrement_user'):
            if team_ids:
                member_ids = self.env['res.users'].search([('sale_team_id', 'in', team_ids)]).ids
                team_domain = ['|', ('user_id', 'in', member_ids), ('team_id', 'in', team_ids)]
            else:
                # Regular user: Only see own leads
                team_domain = [('user_id', '=', user.id)]
        # Merge with existing domain
        domain = domain or []
        domain = ['&'] + domain + team_domain if domain else team_domain

        return super(Recouvrement, self).search_read(domain, fields, offset, limit, order)

    @api.model
    def create_from_sale_orders(self):
        """Create recouvrement records from sale orders with opportunity_id and delivery status done or partially."""
        # Search for sale orders with opportunity_id
        domain = [
            ('opportunity_id', '!=', False),
            ('delivery_state', 'in', ['done', 'partially']),
        ]
        
        sale_orders = self.env['sale.order'].search(domain)
        created_count = 0

        for order in sale_orders:
            # Check if order already has a recouvrement
            existing_recouvrement = self.search([('order_id', '=', order.id)], limit=1)
            if existing_recouvrement:
                continue
                
            # Create recouvrement record
            self.create({
                'partner_id': order.partner_id.id,
                'order_id': order.id,
                'observation': f'Auto-created from sale order {order.name}'
            })
            created_count += 1

        return True

        
