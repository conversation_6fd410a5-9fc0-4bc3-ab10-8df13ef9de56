# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_batch_payment
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <baskhu<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2019
# Khishig<PERSON>d <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-23 11:38+0000\n"
"PO-Revision-Date: 2019-08-26 09:34+0000\n"
"Last-Translator: <PERSON>, 2019\n"
"Language-Team: Mongolian (https://www.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_download_wizard
msgid "Account Batch download wizard"
msgstr "Багц төлөлтийн файл үүсгэх"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Дансны тулгалтын модел"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "All payments in the batch must belong to the same company."
msgstr "Багцлах гэж буй төлбөрүүд нь бүгд ижил компаных байх ёстой."

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "All payments in the batch must share the same payment method."
msgstr "Багцлах гэж буй төлбөрүүд нь бүгд ижил төлбөрийн аргатай байх ёстой."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Amount"
msgstr "Дүн"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__available_payment_method_ids
msgid "Available Payment Method"
msgstr "Боломжит төлбөрийн арга"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__journal_id
msgid "Bank"
msgstr "Банк"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Bank Journal"
msgstr "Банкны журнал"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Batch Content"
msgstr "Багцын агуулга"

#. module: account_batch_payment
#: model:account.payment.method,name:account_batch_payment.account_payment_method_batch_deposit
#: model_terms:ir.ui.view,arch_db:account_batch_payment.account_journal_dashboard_kanban_view_inherited
msgid "Batch Deposit"
msgstr "Багц төлбөр"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_report_account_batch_payment_print_batch_payment
msgid "Batch Deposit Report"
msgstr "Багц төлбөрийн тайлан"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__batch_payment_id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__batch_payment_id
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Batch Payment"
msgstr "Багц төлбөр"

#. module: account_batch_payment
#. openerp-web
#: code:addons/account_batch_payment/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_in
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_out
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_purchases
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_sales
#, python-format
msgid "Batch Payments"
msgstr "Багц төлөлт"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__batch_type
msgid "Batch Type"
msgstr "Багцын төрөл"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__batch_payment_id
msgid "Batch payment from which the file has been generated."
msgstr "Багц төлбөр бүхий файл боловсруулагдаж үүслээ."

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid ""
"Batch payments allow you grouping different payments to ease\n"
"                    reconciliation. They are also useful when depositing checks\n"
"                    to the bank."
msgstr ""
"Багц төлөлт нь танд ирсэн төлбөрүүдийг хялбархан багцалж тулгалт хийх боломж олгоно.\n"
"                   Мөн энэ боломжыг ашиглан та өөрийн төлж буй төлбөрүүдийг багцлан банк руу явуулах боломжтой."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Click here to download the generated file:"
msgstr "Энд дарж үүссэн файлыг татаж авна:"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Close"
msgstr "Хаах"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_code
msgid "Code"
msgstr "Код"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Create Batch Payment"
msgstr "Багц төлбөр үүсгэх"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
msgid "Create a new customer batch payment"
msgstr "Захиалагчийн багц төлбөр шинээр үүсгэх"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid "Create a new vendor batch payment"
msgstr "Нийлүүлэгчийн багц төлбөр шинээр үүсгэх"

#. module: account_batch_payment
#: model:ir.actions.server,name:account_batch_payment.action_account_create_batch_payment
msgid "Create batch payment"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Creation date of the related export file."
msgstr "Экспорт файлын үүссэн огноо."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__currency_id
msgid "Currency"
msgstr "Валют"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Customer"
msgstr "Захиалагч"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__date
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Date"
msgstr "Огноо"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_report_account_batch_payment_print_batch_payment__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.download_xml_form
msgid "Download export file"
msgstr "Экспорт файл татах"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file
msgid "Export file related to this batch"
msgstr "Энэ багцаас боловсруулагдсан файл"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Exported File"
msgstr "Боловсруулсан файл"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__export_file
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file
msgid "File"
msgstr "Файл"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid "File Generation Enabled"
msgstr "Файл үүсгэлт идэвхижсэн"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_filename
msgid "File Name"
msgstr "Файлын нэр"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__export_filename
msgid "File name"
msgstr "Файлын нэр"

#. module: account_batch_payment
#. openerp-web
#: code:addons/account_batch_payment/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter..."
msgstr "Шүүлтүүр..."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__export_file
msgid "Generated XML file"
msgstr "Боловсруулсан XML файл"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Generation Date"
msgstr "Боловсруулсан огоо"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Group By"
msgstr "Бүлэглэлт"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__id
#: model:ir.model.fields,field_description:account_batch_payment.field_report_account_batch_payment_print_batch_payment__id
msgid "ID"
msgstr "ID"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "Id: %s"
msgstr ""

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__inbound
msgid "Inbound"
msgstr "Ирж буй"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Inbound Batch Payments Sequence"
msgstr "Ирж буй багц төлбөрийн дугаарлалт"

#. module: account_batch_payment
#. openerp-web
#: code:addons/account_batch_payment/static/src/js/account_batch_payment_reconciliation.js:0
#, python-format
msgid "Incorrect Operation"
msgstr "Буруу үйлдэл"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_journal
msgid "Journal"
msgstr "Журнал"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment____last_update
#: model:ir.model.fields,field_description:account_batch_payment.field_report_account_batch_payment_print_batch_payment____last_update
msgid "Last Modified on"
msgstr "Сүүлд зассан огноо"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Memo"
msgstr "Санамж"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_filename
msgid "Name of the export file generated for this batch"
msgstr "Энэ багцаас боловсруулагдаж гарах файлын нэр"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__export_filename
msgid "Name of the generated XML file"
msgstr "Боловсруулсан XML файлын нэр"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__draft
msgid "New"
msgstr "Шинэ"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__outbound
msgid "Outbound"
msgstr "Гарч буй"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_journal.py:0
#, python-format
msgid "Outbound Batch Payments Sequence"
msgstr "Гарч буй багц төлбөрийн дугаарлалт"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "Payment Method"
msgstr "Төлбөрийн арга"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_ids
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payments"
msgstr "Төлбөрүүд"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Print"
msgstr "Хэвлэх"

#. module: account_batch_payment
#: model:ir.actions.report,name:account_batch_payment.action_print_batch_payment
msgid "Print Batch Payment"
msgstr "Багц төлбөр хэвлэх"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__reconciled
msgid "Reconciled"
msgstr "Тулгагдсан"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__name
msgid "Reference"
msgstr "Холбогдол"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Register Payment"
msgstr "Төлбөр бүртгэх"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__sent
msgid "Sent"
msgstr "Илгээгдсэн"

#. module: account_batch_payment
#. openerp-web
#: code:addons/account_batch_payment/static/src/js/account_batch_payment_reconciliation.js:0
#, python-format
msgid ""
"Some journal items from the selected batch payment are already selected in "
"another reconciliation : "
msgstr ""
"Сонгосон багц төлөлтийн зарим журналын бичилтүүд аль хэдийнээ өөр гүйлгээтэй"
" тулгагдсан байна : "

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__state
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "State"
msgstr "Төлөв"

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"The batch must have the same payment method as the payments it contains."
msgstr ""
"Багцад агуулагдаж буй төлбөрүүд бүгд ижил төлбөрийн аргатай байх ёстой."

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid "The batch must have the same type as the payments it contains."
msgstr ""
"Багцад агуулагдаж буй төлбөрүүд бүгд ижил төлбөрийн төрөлтэй байх ёстой."

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"The journal of the batch payment and of the payments it contains must be the"
" same."
msgstr "Багцад агуулагдаж буй төлбөрүүд бүгд ижил журналтай байх ёстой."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "The payment method used by the payments in this batch."
msgstr "Энэ багцын төлбөрүүдэд хэрэглэгдэх төлбөрийн арга."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Total"
msgstr "Нийт"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Unreconciled"
msgstr "Тулгагдаагүй"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Validate"
msgstr "Батлах"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_download_wizard__warning_message
msgid "Warning"
msgstr "Анхааруулга"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_download_wizard__warning_message
msgid "Warning message to display about the content of the downloadable file."
msgstr "Татаж авах файлын агуулгын тухай анхааруулах мессеж."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid ""
"Whether or not this batch payment should display the 'Generate File' button "
"instead of 'Print' in form view."
msgstr ""
"Энэ багцын дэлгэц дээр 'Хэвлэх' товчын оронд 'Файл боловсруулах' товч "
"харуулах эсэхийг илтгэнэ."

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#, python-format
msgid ""
"You cannot add payments with zero amount in a Batch Payment.\n"
"Payments:\n"
"%s"
msgstr ""

#. module: account_batch_payment
#: code:addons/account_batch_payment/models/account_payment.py:0
#, python-format
msgid ""
"You cannot make a batch payment with internal transfers. Internal transfers "
"ids: %s"
msgstr ""

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "auto ..."
msgstr "авто ..."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "re-Generate Export File"
msgstr "экспорт файлыг дахин боловсруулах"
