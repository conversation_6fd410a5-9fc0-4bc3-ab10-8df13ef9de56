<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_after_sale_visite_tree" model="ir.ui.view">
        <field name="name">after.sale.visite.tree</field>
        <field name="model">after.sale.visite</field>
        <field name="arch" type="xml">
            <tree string="After Sale Visits">
                <field name="service_id"/>
                <field name="partner_id"/>
                <field name="crm_id"/>
                <field name="counter_id"/>
                <field name ="user_id"/>
                <field name="order_id"/>
                <field name="installation_type"/>
                <field name="date_visite"/>
                <field name="puiss_equip"/>
                <field name="puissance_installee"/>
                <field name="panneaux_installe"/>
                <field name="nb_panneaux"/>
                <field name="nb_coffret_ac"/>
                <field name="nb_coffret_dc"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_after_sale_visite_form" model="ir.ui.view">
        <field name="name">after.sale.visite.form</field>
        <field name="model">after.sale.visite</field>
        <field name="arch" type="xml">
            <form string="After Sale Visit">
                <sheet>
                    <group>
                        <group>
                            <field name="service_id"/>
                            <field name="partner_id"/>
                            <field name="crm_id"/>
                            <field name="counter_id"/>
                            <field name="order_id"/>
                            <field name ="user_id"/>
                            <field name="installation_type"/>
                        </group>
                        <group>
                            <field name="puiss_equip"/>
                            <field name="puissance_installee"/>
                            <field name="panneaux_installe"/>
                            <field name="nb_panneaux"/>
                            <field name="nb_coffret_ac"/>
                            <field name="nb_coffret_dc"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Visit Details">
                            <field name="detail_visite_ids">
                                <tree editable="bottom">
                                    <field name="date_visite"/>
                                    <field name="rappo_visite" filename="rapport_name"/>
                                    <button name="action_preview_attachment" icon="fa-eye" string="" type="object" class="btn-primary"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Suivi Labos">
                            <field name="suivi_labo_ids" nolabel="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
        <!-- Search View -->
        <record id="view_after_sale_visite_search" model="ir.ui.view">
            <field name="name">after.sale.visite.search</field>
            <field name="model">after.sale.visite</field>
            <field name="arch" type="xml">
                <search string="Search After Sale Visits">
                    <field name="service_id"/>
                    <field name="partner_id"/>
                    <field name="crm_id"/>
                    <field name="counter_id"/>
                    <field name="order_id"/>
                    <field name="installation_type"/>
                    <field name="date_visite"/>
                    <filter string="Connected to Networks" name="connected" domain="[('installation_type', '=', 'connected')]"/>
                    <filter string="Pumping Project" name="pumping_project" domain="[('installation_type', '=', 'pumping_project')]"/>
                    <group expand="0" string="Group By">
                        <filter string="Partner" name="group_by_partner" context="{'group_by': 'partner_id'}"/>
                        <filter string="Service" name="group_by_service" context="{'group_by': 'service_id'}"/>
                        <filter string="Installation Type" name="group_by_installation" context="{'group_by': 'installation_type'}"/>
                        <filter string="Date" name="group_by_date" context="{'group_by': 'date_visite'}"/>
                        <filter string="Order" name="group_by_order" context="{'group_by': 'order_id'}"/>
                    </group>
                </search>
            </field>
        </record>
    
        <!-- Action -->
        <record id="action_after_sale_visite" model="ir.actions.act_window">
            <field name="name">Visites</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">after.sale.visite</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_after_sale_visite_search"/>
        </record>

    <!-- Menu -->
    <menuitem id="menu_after_sale_visite"
        name="Visites"
        parent="after_sale_service.menu_after_sale_service_root"
        action="action_after_sale_visite"
        sequence="20"/>

        <!-- Action for My visites -->
    <record id="action_my_after_sale_visite" model="ir.actions.act_window">
        <field name="name">Mes Visites</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">after.sale.visite</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('user_id', '=', uid)]</field>
        <field name="search_view_id" ref="view_after_sale_visite_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No visites assigned to you yet!
            </p>
        </field>
    </record>
<!-- Menu Item for My visites under CRM menu -->
<menuitem id="menu_my_after_sale_visite"
name="Mes Visites"
parent="crm.crm_menu_root"
action="action_my_after_sale_visite"
sequence="20"/>
</odoo>