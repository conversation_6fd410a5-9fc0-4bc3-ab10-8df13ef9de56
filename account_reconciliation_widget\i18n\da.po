# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reconciliation_widget
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-10-09 09:13+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/reconciliation_widget.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "En udligning skal involvere mindst 2 posteringer."

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/account_bank_statement.py:0
#, python-format
msgid "A selected move line was already reconciled."
msgstr "En valgt postering var allerede udlignet."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Account"
msgstr "Konto"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Konto udlignings widget"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr ""
"Alle fakturaer og betalinger er blevet matcet, dine kontis balance er ren."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Amount"
msgstr "Beløb"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Acc."
msgstr "Analyse konto"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Tags."
msgstr "Analyse tags."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_action.js:0
#, python-format
msgid "Bank Reconciliation"
msgstr "Bank udligning"

#. module: account_reconciliation_widget
#: model:ir.model.fields,field_description:account_reconciliation_widget.field_res_company__account_bank_reconciliation_start
#: model:ir.model.fields,field_description:account_reconciliation_widget.field_res_config_settings__account_bank_reconciliation_start
msgid "Bank Reconciliation Threshold"
msgstr "Bank udlignings grænse"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_account_bank_statement
msgid "Bank Statement"
msgstr "Bankkontoudtog"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Bank kontoudtogslinie"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check all"
msgstr "Tjek alle"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check that you have no bank statement lines to"
msgstr "Tjek, at du ikke har nogen kontoudtogslinjer til"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "Vælg modpost eller opret afskrivning"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close"
msgstr "Luk"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close statement"
msgstr "Luk kontoudtog"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurations opsætning"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "Tillykke, du er færdig!"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid ""
"Congrats, you're all done! You reconciled %s transactions in %s. That's on "
"average %s seconds per transaction."
msgstr ""
"Tillykke,Du er helt færdig! Ud udlignede %s transaktioner i %s. Det er "
"gennemsnitlig %s sekunder per transaktion."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create a counterpart"
msgstr "Opret modpost"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create model"
msgstr "Opret model"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Customer/Vendor Matching"
msgstr "Kunde/Leverandør matching"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Date"
msgstr "Dato"

#. module: account_reconciliation_widget
#: model_terms:ir.ui.view,arch_db:account_reconciliation_widget.res_config_settings_view_form
msgid ""
"Date from which you started using bank statements in Odoo. Leave empty if "
"you've been using bank statements in Odoo from the start."
msgstr ""
"Dato, hvorfra du begyndte at bruge kontoudtog i Odoo. Lad være tomt, hvis du "
"har brugt kontoudtog i Odoo fra starten."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Description"
msgstr "Beskrivelse"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Due Date"
msgstr "Forfaldsdato"

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/account_move.py:0
#, python-format
msgid "Either pass both debit and credit or none."
msgstr "Enten indtast både debet og kredit eller ingen."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "External link"
msgstr "Eksternt link"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr "Filter på konto, etiket, partner, beløb,..."

#. module: account_reconciliation_widget
#: model:ir.model.fields,field_description:account_reconciliation_widget.field_account_bank_statement__accounting_date
msgid "Financial Date"
msgstr "Bogføringsdato"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr "Fra nu af vil du måske:"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Go to bank statement(s)"
msgstr "Gå til kontoudtog"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Good Job!"
msgstr "Godt job!"

#. module: account_reconciliation_widget
#: model:ir.model.fields,help:account_reconciliation_widget.field_account_bank_statement__accounting_date
msgid ""
"If set, the accounting entries created during the bank statement "
"reconciliation process will be created at this date.\n"
"This is useful if the accounting period in which the entries should normally "
"be booked is already closed."
msgstr ""
"Hvis det er angivet, vil de regnskabsposteringer, der er oprettet under "
"afstemningsprocessen for kontoudtog, blive oprettet på denne dato.\n"
"Dette er nyttigt, hvis den regnskabsperiode, hvor posteringerne normalt skal "
"bogføres, allerede er afsluttet."

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/account_move.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""
"Det er obligatorisk at angive en konto og en journal for at oprette en "
"afskrivning."

#. module: account_reconciliation_widget
#: model_terms:ir.ui.view,arch_db:account_reconciliation_widget.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Posteringer"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_reconciliation_widget.model_account_journal
#, python-format
msgid "Journal"
msgstr "Journal"

#. module: account_reconciliation_widget
#: model:ir.model.fields,field_description:account_reconciliation_widget.field_account_bank_statement_line__move_name
msgid "Journal Entry Name"
msgstr "Posteringsnavn"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_account_move_line
msgid "Journal Item"
msgstr "Postering"

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/reconciliation_widget.py:0
#, python-format
msgid "Journal Items"
msgstr "Posteringer"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_action.js:0
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Posteringer til udligning"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Label"
msgstr "Etiket"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr "Seneste udligning:"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more"
msgstr "Indlæs flere"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more... ("
msgstr "Indlæst flere...("

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Manual Operations"
msgstr "Manuel postering"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr "Match med posteringer, der ikke er fra debitor/kreditor konti"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr "Diverse Matching"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Modify models"
msgstr "Ændre modeller"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "New"
msgstr "Ny"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Note"
msgstr "Note"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Nothing to do!"
msgstr "Intet at gøre!"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_model.js:0
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Open balance"
msgstr "Åben balance"

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/account_bank_statement.py:0
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number "
"(%s), you cannot reconcile it entirely with existing journal entries "
"otherwise it would make a gap in the numbering. You should book an entry and "
"make a regular revert of it in case you want to cancel it."
msgstr ""
"Handling ikke tilladt. Da din kontolinje allerede har modtaget et nummer "
"(%s), kan du ikke afstemme det helt med eksisterende journalposteringer, "
"ellers ville det lave et hul i nummereringen. Du bør reservere en adgang og "
"foretage en regelmæssig tilbagevenden af den, hvis du ønsker at annullere "
"den."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Partner"
msgstr "Partner"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Pay your"
msgstr "Betal dig"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Presets config"
msgstr "Forudindstillet konfiguration"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.client,name:account_reconciliation_widget.action_view_account_move_line_reconcile
#: model_terms:ir.ui.view,arch_db:account_reconciliation_widget.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_reconciliation_widget.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr "Udlign"

#. module: account_reconciliation_widget
#: model:ir.actions.client,name:account_reconciliation_widget.action_manual_reconciliation
#: model:ir.ui.menu,name:account_reconciliation_widget.menu_action_manual_reconciliation
msgid "Reconciliation"
msgstr "Udligning"

#. module: account_reconciliation_widget
#: model:ir.actions.client,name:account_reconciliation_widget.action_bank_reconcile
msgid "Reconciliation on Bank Statements"
msgstr "Udligning for bank kontoudtog"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Ref"
msgstr "Ref"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Residual"
msgstr "Resterende"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Save and New"
msgstr "Gem og ny"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Select Partner"
msgstr "Vælg partner"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Vælg partner eller vælg modpost"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Settings"
msgstr "Opsætning"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Skip"
msgstr "Skip"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Some fields are undefined"
msgstr "Nogle felter er udefinerede"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Tax Included in Price"
msgstr "Moms includeret i prisen"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Taxes"
msgstr "Afgifter"

#. module: account_reconciliation_widget
#: model:ir.model.fields,help:account_reconciliation_widget.field_account_bank_statement_line__move_name
msgid ""
"Technical field holding the number given to the journal entry,automatically "
"set when the statement line is reconciled then storedto set the same number "
"again if the line is cancelled,set to draft and re-processed again."
msgstr ""
"Teknisk felt, der indeholder nummeret, der er givet til bilagsposten, "
"indstilles automatisk, når opgørelseslinjen afstemmes og gemmes for at "
"indstille det samme nummer igen, hvis linjen annulleres, indstilles til "
"kladde og genbehandles igen."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "That's on average"
msgstr "Det er i gennemsnit"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr "Beløbet %s er ikke et gyldigt delbeløb"

#. module: account_reconciliation_widget
#: model:ir.model.fields,help:account_reconciliation_widget.field_res_company__account_bank_reconciliation_start
#: model:ir.model.fields,help:account_reconciliation_widget.field_res_config_settings__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than "
"this date.\n"
"This is useful if you install accounting after having used invoicing for "
"some time and don't want to reconcile all the past payments with bank "
"statements."
msgstr ""
"Bankafstemningswidgetten vil ikke bede om at afstemme betalinger, der er "
"ældre end denne dato.\n"
"Dette er nyttigt, hvis du installerer regnskab efter at have brugt "
"fakturering i nogen tid og ikke ønsker at afstemme alle tidligere betalinger "
"med kontoudtog."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr "Der er ikke noget at udligne."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"Denne side viser alle de banktransaktioner, der skal afstemmes, og har en "
"pæn grænseflade til at gøre det."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Denne betaling er registreret men ikke udlignet."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To Check"
msgstr "Til tjek"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr "For at fremskynde afstemning skal du definere"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Transaction"
msgstr "Transaktion"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Validate"
msgstr "Validér"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Verify"
msgstr "Verificere"

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/account_move.py:0
#, python-format
msgid "Write-Off"
msgstr "Afskriv"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Writeoff Date"
msgstr "Afskrivningsdato"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "You reconciled"
msgstr "Du udlignede"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "and follow-up customers"
msgstr "og opfølgende kunder"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "have been reconciled automatically."
msgstr "er blevet udlignet automatisk."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconcile"
msgstr "Udlign"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconciliation models"
msgstr "Udligningsmodeller"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "remaining)"
msgstr "tilbage)"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "sekunder pr transkation."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "statement lines"
msgstr "kontoudtogslinijer"

#. module: account_reconciliation_widget
#: model_terms:ir.ui.view,arch_db:account_reconciliation_widget.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "til tjek"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "transactions in"
msgstr "transaktioner i"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unpaid invoices"
msgstr "Ubetalte fakturaer"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unreconciled entries"
msgstr "Ikke-udlignet linjer"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "vendor bills"
msgstr "Leverandørfakturaer"
