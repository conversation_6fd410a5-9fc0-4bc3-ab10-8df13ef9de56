<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2023 ForgeFlow S.L.
     License LGPL-3.0 or later (http://www.gnu.org/licenses/lgpl).
 -->
<odoo noupdate="1">
    <record model="ir.cron" id="cron_sale_validity_auto_cancel">
        <field name="name">Cancel Expired Quotations</field>
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
        <field name="active">False</field>
        <field name="doall" eval="False" />
        <field name="model_id" ref="model_sale_order" />
        <field name="state">code</field>
        <field name="code">model.cron_sale_validity_auto_cancel()</field>
    </record>
</odoo>
