Stack trace:
Frame         Function      Args
0007FFFFABC0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF9AC0) msys-2.0.dll+0x2118E
0007FFFFABC0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFAE98) msys-2.0.dll+0x69BA
0007FFFFABC0  0002100469F2 (00021028DF99, 0007FFFFAA78, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFABC0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFABC0  00021006A545 (0007FFFFABD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFAEA0  00021006B9A5 (0007FFFFABD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCB0980000 ntdll.dll
7FFCAF770000 KERNEL32.DLL
7FFCADCE0000 KERNELBASE.dll
7FFCAF1C0000 USER32.dll
7FFCAE6F0000 win32u.dll
7FFCAF860000 GDI32.dll
7FFCAE5B0000 gdi32full.dll
7FFCAE310000 msvcp_win.dll
7FFCAE3C0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCAFA10000 advapi32.dll
7FFCB02D0000 msvcrt.dll
7FFCAFAD0000 sechost.dll
7FFCAE830000 RPCRT4.dll
7FFCACFE0000 CRYPTBASE.DLL
7FFCAE510000 bcryptPrimitives.dll
7FFCAF4A0000 IMM32.DLL
