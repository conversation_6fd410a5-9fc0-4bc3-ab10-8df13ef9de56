<?xml version="1.0" encoding="utf-8" ?>
<!--Copyright 2017-20 ForgeFlow S.L. (http://www.forgeflow.com)-->
<!--License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl.html).-->
<odoo>
    <record id="res_partner_view_buttons" model="ir.ui.view">
        <field name="name">res.partner.view.buttons</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form" />
        <field name="priority" eval="20" />
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]" />
        <field name="arch" type="xml">
            <div name="button_box" position="inside">
                <button
                    class="oe_stat_button"
                    type="object"
                    name="action_open_reconcile"
                    context="{'reconcile_mode': 'customers'}"
                    icon="fa-usd"
                    string="Match Receivables"
                >
                </button>
                <button
                    class="oe_stat_button"
                    type="object"
                    name="action_open_reconcile"
                    context="{'reconcile_mode': 'suppliers'}"
                    icon="fa-usd"
                    string="Match Payables"
                >
                </button>
            </div>
        </field>
    </record>
</odoo>
