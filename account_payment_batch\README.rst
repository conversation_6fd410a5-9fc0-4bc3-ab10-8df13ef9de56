================================
Account Payment Batch Processing
================================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:6468017331e8d45e6095d230fb4db7aa63ab5f6e817ea519127a39485fab8e06
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Faccount--payment-lightgray.png?logo=github
    :target: https://github.com/OCA/account-payment/tree/15.0/account_payment_batch_process
    :alt: OCA/account-payment
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/account-payment-15-0/account-payment-15-0-account_payment_batch_process
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/account-payment&target_branch=15.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

Batch Payments Processing for Customers Invoices and Supplier Invoices/Vendor Bills.

Payments to multiple vendors can be processed. Payments from multiple customers can be processed.

Partial payments can be processed leaving invoice/bill open or writing off unpaid portion to a write-off account.

**Table of contents**

.. contents::
   :local:

Changelog
=========

********.0
~~~~~~~~~~

**Features**

- Remove menu "Batch Payments" from Vendors and Customers
- Remove menu "Register Payment" from batch invoices.

********.0
~~~~~~~~~~

- Initial File

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/account-payment/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/account-payment/issues/new?body=module:%20account_payment_batch_process%0Aversion:%2015.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* Open Source Integrators

Contributors
~~~~~~~~~~~~

* Balaji Kannan <<EMAIL>>
* Bhavesh Odedra <<EMAIL>>
* Mayank Gosai <<EMAIL>>
* Sandip Mangukiya <<EMAIL>>
* Hardik Suthar <<EMAIL>>

* Camptocamp <https://www.camptocamp.com>
  * Denis Leemann <<EMAIL>>

Other credits
~~~~~~~~~~~~~

* Open Source Integrators <<EMAIL>>

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/account-payment <https://github.com/OCA/account-payment/tree/15.0/account_payment_batch_process>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
