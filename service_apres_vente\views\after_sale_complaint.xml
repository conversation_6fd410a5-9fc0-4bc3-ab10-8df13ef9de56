<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_after_sale_complaint_tree" model="ir.ui.view">
        <field name="name">after.sale.complaint.tree</field>
        <field name="model">after.sale.complaint</field>
        <field name="arch" type="xml">
            <tree string="After Sale Complaints">
                <field name="service_id"/>
                <field name="name"/>
                <field name="partner_id"/>
                <field name ="user_id"/>
                <field name="order_id"/>
                <field name="installation_type"/>
                <field name="date_reclamation"/>
                <field name="telephone"/>
                <field name="etat_avancement"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_after_sale_complaint_form" model="ir.ui.view">
        <field name="name">after.sale.complaint.form</field>
        <field name="model">after.sale.complaint</field>
        <field name="arch" type="xml">
            <form string="After Sale Complaint">
                <header>
                    <field name="etat_avancement" widget="statusbar" options="{'clickable': '1', 'fold_field': 'fold'}"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="service_id"/>
                            <field name="name"/>
                            <field name="partner_id"/>
                            <field name ="user_id"/>
                            <field name="order_id"/>
                            <field name="installation_type"/>
                            <field name="date_reclamation"/>
                            <field name="telephone"/>
                            <field name="puissance_installee"/>
                        </group>
                        <group>
                            <field name="nb_panneaux"/>
                            <field name="puiss_equip"/>
                            <field name="post_installation_sending_date"/>
                            <field name="date_reponse"/>
                            <field name="temps_reponse"/>
                            <field name="traite_par"/>
                            <field name="rapport_reclamation"/>
                            <field name="retour_telep"/>
                            <field name="date_retour"/>

                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <group>
                                <field name="description_reclamation" nolabel="1" placeholder="Description de la réclamation..."/>
                                <field name="photos_reclamation_ids" widget="many2many_binary"/>
                            </group>
                        </page>
                        <page string="Intervention">
                            <group>
                                <field name="intervention_effectuee" nolabel="1" placeholder="Intervention effectuée..."/>
                                <field name="photos_apres_intervention_ids" widget="many2many_binary"/>
                            </group>
                        </page>
                        <page string="Suivi Labos">
                            <field name="suivi_labo_ids" nolabel="1"/>
                        </page>
                        <page string="Recommendation">
                            <field name="recommendation" nolabel="1"/>
                        </page>

                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    
        <!-- Search View -->
        <record id="view_after_sale_complaint_search" model="ir.ui.view">
            <field name="name">after.sale.complaint.search</field>
            <field name="model">after.sale.complaint</field>
            <field name="arch" type="xml">
                <search string="Search After Sale Complaints">
                    <field name="service_id"/>
                    <field name="partner_id"/>
                    <field name="order_id"/>
                    <field name="installation_type"/>
                    <field name="date_reclamation"/>
                    <field name="telephone"/>
                    <field name="traite_par"/>
                    <field name ="user_id"/>
                    <filter string="En Attente" name="en_attente" domain="[('etat_avancement', '=', 'en_attente')]"/>
                    <filter string="En Cours" name="en_cours" domain="[('etat_avancement', '=', 'en_cours')]"/>
                    <filter string="Terminé" name="termine" domain="[('etat_avancement', '=', 'termine')]"/>
                    <group expand="0" string="Group By">
                        <filter string="Partner" name="group_by_partner" context="{'group_by': 'partner_id'}"/>
                        <filter string="Service" name="group_by_service" context="{'group_by': 'service_id'}"/>
                        <filter string="Installation Type" name="group_by_installation" context="{'group_by': 'installation_type'}"/>
                        <filter string="Date de Réclamation" name="group_by_date" context="{'group_by': 'date_reclamation'}"/>
                        <filter string="État d'avancement" name="group_by_etat" context="{'group_by': 'etat_avancement'}"/>
                    </group>
                </search>
            </field>
        </record>
    
        <!-- Action -->
        <record id="action_after_sale_complaint" model="ir.actions.act_window">
            <field name="name">Réclamations Après Vente</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">after.sale.complaint</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_after_sale_complaint_search"/>
        </record>

    <!-- Menu -->
    <menuitem id="menu_after_sale_complaint"
        name="Réclamations"
        parent="after_sale_service.menu_after_sale_service_root"
        action="action_after_sale_complaint"
        sequence="10"/>


    <!-- Action for My Complaints -->
    <record id="action_my_after_sale_complaint" model="ir.actions.act_window">
        <field name="name">Mes Réclamations</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">after.sale.complaint</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('user_id', '=', uid)]</field>
        <field name="search_view_id" ref="view_after_sale_complaint_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No complaints assigned to you yet!
            </p>
        </field>
    </record>
<!-- Menu Item for My Complaints under CRM menu -->
<menuitem id="menu_my_after_sale_complaint"
name="Mes Réclamations"
parent="crm.crm_menu_root"
action="action_my_after_sale_complaint"
sequence="20"/>
</odoo>