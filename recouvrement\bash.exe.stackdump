Stack trace:
Frame         Function      Args
0007FFFF9F80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8E80) msys-2.0.dll+0x2118E
0007FFFF9F80  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFA258) msys-2.0.dll+0x69BA
0007FFFF9F80  0002100469F2 (00021028DF99, 0007FFFF9E38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9F80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9F80  00021006A545 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFA260  00021006B9A5 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC63180000 ntdll.dll
7FFC62F40000 KERNEL32.DLL
7FFC60360000 KERNELBASE.dll
7FFC62CC0000 USER32.dll
7FFC607D0000 win32u.dll
7FFC611F0000 GDI32.dll
7FFC60E40000 gdi32full.dll
7FFC60CD0000 msvcp_win.dll
7FFC60AF0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC622D0000 advapi32.dll
7FFC61760000 msvcrt.dll
7FFC63090000 sechost.dll
7FFC61640000 RPCRT4.dll
7FFC5F860000 CRYPTBASE.DLL
7FFC60730000 bcryptPrimitives.dll
7FFC626A0000 IMM32.DLL
