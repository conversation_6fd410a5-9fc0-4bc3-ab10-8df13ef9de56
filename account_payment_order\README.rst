=====================
Account Payment Order
=====================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:5be4eaab2e730e5f7f3515267c1bb4484ac2d521142af86f98e6bd5d389394f6
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fbank--payment-lightgray.png?logo=github
    :target: https://github.com/OCA/bank-payment/tree/15.0/account_payment_order
    :alt: OCA/bank-payment
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/bank-payment-15-0/bank-payment-15-0-account_payment_order
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/bank-payment&target_branch=15.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module adds support for payment orders and debit orders.

**Table of contents**

.. contents::
   :local:

Installation
============

This module depends on:

* account_payment_partner
* base_iban
* document

This modules is part of the OCA/bank-payment suite.

Configuration
=============

This module adds several options on Payment Modes, cf Invoicing/Accounting >
Configuration > Management > Payment Modes.

Usage
=====

You can create a Payment order via the menu Invoicing/Accounting > Vendors > Payment Orders and then select the move lines to pay.

You can create a Debit order via the menu Invoicing/Accounting > Customers > Debit Orders and then select the move lines to debit.

This module also adds an action *Add to Payment Order* on supplier invoices and *Add to Debit Order* on customer invoices.

You can print a Payment order via the menu Invoicing/Accounting > Vendors > Payment Orders and then select the payment oder to print.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/bank-payment/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/bank-payment/issues/new?body=module:%20account_payment_order%0Aversion:%2015.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* ACSONE SA/NV
* Therp BV
* Tecnativa
* Akretion

Contributors
~~~~~~~~~~~~

* Stéphane Bidoul <<EMAIL>>
* Alexis de Lattre <<EMAIL>>
* Adrien Peiffer <<EMAIL>>
* Stefan Rijnhart
* Laurent Mignon <<EMAIL>>
* Alexandre Fayolle
* Danimar Ribeiro
* Erwin van der Ploeg
* Raphaël Valyi
* Sandy Carter
* Angel Moya <<EMAIL>>
* Jose María Alzaga <<EMAIL>>
* Meyomesse Gilles <<EMAIL>>
* Denis Roussel <<EMAIL>>

* `DynApps <https://www.dynapps.be>`_:

  * Raf Ven <<EMAIL>>
* Andrea Stirpe <<EMAIL>>
* `Jarsa <https://www.jarsa.com.mx>`_:

  * Alan Ramos <<EMAIL>>
* `Tecnativa <https://www.tecnativa.com>`_:

  * Pedro M. Baeza
  * Carlos Dauden
  * Carlos Roca

* `Open Source Integrators <https://www.opensourceintegrators.com>`_:

  * Ammar Officewala <<EMAIL>>
* Marçal Isern <<EMAIL>>

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/bank-payment <https://github.com/OCA/bank-payment/tree/15.0/account_payment_order>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
