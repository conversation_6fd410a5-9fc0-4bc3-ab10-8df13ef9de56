<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2018 <PERSON><PERSON> <<EMAIL>>
     License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl). -->
<odoo>
    <record id="account_payment_mode_form" model="ir.ui.view">
        <field name="model">account.payment.mode</field>
        <field name="inherit_id" ref="account_payment_mode.account_payment_mode_form" />
        <field name="arch" type="xml">
            <field name="variable_journal_ids" position="before">
                <field name="refund_payment_mode_id" />
            </field>
            <group name="note" position="before">
                <group string="Show bank account in invoice report">
                    <group>
                        <field name="show_bank_account" />
                        <field
                            name="show_bank_account_from_journal"
                            attrs="{'invisible': [('show_bank_account', '=', 'no')]}"
                        />
                        <field
                            name="show_bank_account_chars"
                            string="# of chars"
                            attrs="{'invisible': [('show_bank_account', 'not in', ['first', 'last'])]}"
                        />
                    </group>
                </group>
            </group>
        </field>
    </record>
</odoo>
