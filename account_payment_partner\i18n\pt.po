# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment_partner
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2021-10-25 22:36+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_payment_partner
#: model_terms:ir.ui.view,arch_db:account_payment_partner.account_payment_mode_form
msgid "# of chars"
msgstr "Número de caracteres"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_payment_mode__show_bank_account_chars
msgid "# of digits for customer bank account"
msgstr "Nº. de dígitos para utilizar na conta bancária do cliente"

#. module: account_payment_partner
#: model_terms:ir.ui.view,arch_db:account_payment_partner.report_invoice_payment_mode
msgid "<strong>Bank Account:</strong>"
msgstr "<strong>Conta Bancária:</strong>"

#. module: account_payment_partner
#: model_terms:ir.ui.view,arch_db:account_payment_partner.report_invoice_payment_mode
msgid "<strong>Payment Mode:</strong>"
msgstr "<strong>Modo de pagamento: </strong>"

#. module: account_payment_partner
#: model:ir.model.fields,help:account_payment_partner.field_account_bank_statement_line__bank_account_required
#: model:ir.model.fields,help:account_payment_partner.field_account_move__bank_account_required
#: model:ir.model.fields,help:account_payment_partner.field_account_payment__bank_account_required
msgid ""
"Activate this option if this payment method requires you to know the bank "
"account number of your customer or supplier."
msgstr ""
"Ative esta opção de pagamento se este método de pagamento o obriga a "
"conhecer o número de conta bancária do seu cliente ou fornecedor."

#. module: account_payment_partner
#: model:ir.model.fields,help:account_payment_partner.field_account_bank_statement_line__partner_bank_id
#: model:ir.model.fields,help:account_payment_partner.field_account_move__partner_bank_id
msgid ""
"Bank Account Number to which the invoice will be paid. A Company bank "
"account if this is a Customer Invoice or Vendor Credit Note, otherwise a "
"Partner bank account number."
msgstr ""
"Número da Conta Bancária na qual se vai pagar a fatura. Conta bancária da "
"Empresa se se tratar de uma Fatura de Cliente ou Nota de Crédito "
"de Fornecedor. Caso contrário, número de conta bancária de um Parceiro."

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_bank_statement_line__bank_account_required
#: model:ir.model.fields,field_description:account_payment_partner.field_account_move__bank_account_required
#: model:ir.model.fields,field_description:account_payment_partner.field_account_payment__bank_account_required
msgid "Bank Account Required"
msgstr "Conta Bancária Requerida"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_payment_mode__show_bank_account_from_journal
msgid "Bank account from journals"
msgstr "Conta Bancária dos diários"

#. module: account_payment_partner
#: model:ir.model,name:account_payment_partner.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_res_partner__customer_payment_mode_id
#: model:ir.model.fields,field_description:account_payment_partner.field_res_users__customer_payment_mode_id
msgid "Customer Payment Mode"
msgstr "Modo de Pagamento do Cliente"

#. module: account_payment_partner
#: model:ir.model.fields.selection,name:account_payment_partner.selection__account_payment_mode__show_bank_account__first
msgid "First n chars"
msgstr "Primeiros n caracteres"

#. module: account_payment_partner
#: model:ir.model.fields.selection,name:account_payment_partner.selection__account_payment_mode__show_bank_account__full
msgid "Full"
msgstr "Completo"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_bank_statement_line__has_reconciled_items
#: model:ir.model.fields,field_description:account_payment_partner.field_account_move__has_reconciled_items
#: model:ir.model.fields,field_description:account_payment_partner.field_account_payment__has_reconciled_items
msgid "Has Reconciled Items"
msgstr ""

#. module: account_payment_partner
#: model:ir.model,name:account_payment_partner.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Estatísticas de Faturas"

#. module: account_payment_partner
#: model:ir.model,name:account_payment_partner.model_account_move
msgid "Journal Entry"
msgstr "Movimento de Diário"

#. module: account_payment_partner
#: model:ir.model,name:account_payment_partner.model_account_move_line
msgid "Journal Item"
msgstr "Item do Diário"

#. module: account_payment_partner
#: model:ir.model.fields.selection,name:account_payment_partner.selection__account_payment_mode__show_bank_account__last
msgid "Last n chars"
msgstr "Últimos n caracteres"

#. module: account_payment_partner
#: model:ir.model.fields.selection,name:account_payment_partner.selection__account_payment_mode__show_bank_account__no
msgid "No"
msgstr "Não"

#. module: account_payment_partner
#: code:addons/account_payment_partner/tests/test_account_payment_partner.py:0
#, python-format
msgid "No Chart of Account Template has been defined !"
msgstr "Não foi definida nenhum Modelo de Plano de Contas !"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_bank_statement_line__partner_bank_filter_type_domain
#: model:ir.model.fields,field_description:account_payment_partner.field_account_move__partner_bank_filter_type_domain
#: model:ir.model.fields,field_description:account_payment_partner.field_account_payment__partner_bank_filter_type_domain
msgid "Partner Bank Filter Type Domain"
msgstr "Domínio de Tipo de Filtro de Banco de Parceiro"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_bank_statement_line__payment_mode_id
#: model:ir.model.fields,field_description:account_payment_partner.field_account_move__payment_mode_id
#: model:ir.model.fields,field_description:account_payment_partner.field_account_move_line__payment_mode_id
#: model:ir.model.fields,field_description:account_payment_partner.field_account_payment__payment_mode_id
#: model_terms:ir.ui.view,arch_db:account_payment_partner.view_account_invoice_filter
#: model_terms:ir.ui.view,arch_db:account_payment_partner.view_account_invoice_report_search
msgid "Payment Mode"
msgstr "Modo de Pagamento"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_bank_statement_line__payment_mode_filter_type_domain
#: model:ir.model.fields,field_description:account_payment_partner.field_account_move__payment_mode_filter_type_domain
#: model:ir.model.fields,field_description:account_payment_partner.field_account_payment__payment_mode_filter_type_domain
msgid "Payment Mode Filter Type Domain"
msgstr "Domínio de Tipo de Filtro de Modo de Pagamento"

#. module: account_payment_partner
#: model:ir.model,name:account_payment_partner.model_account_payment_mode
msgid "Payment Modes"
msgstr "Modos de Pagamento"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_invoice_report__payment_mode_id
msgid "Payment mode"
msgstr "Modo de pagamento"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_payment_mode__refund_payment_mode_id
msgid "Payment mode for refunds"
msgstr "Modo de pagamento para reembolsos"

#. module: account_payment_partner
#: model_terms:ir.ui.view,arch_db:account_payment_partner.view_move_line_form
msgid "Payments"
msgstr "Pagamentos"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_bank_statement_line__partner_bank_id
#: model:ir.model.fields,field_description:account_payment_partner.field_account_move__partner_bank_id
msgid "Recipient Bank"
msgstr "Banco Beneficiário"

#. module: account_payment_partner
#: model:ir.model.fields,help:account_payment_partner.field_res_partner__customer_payment_mode_id
#: model:ir.model.fields,help:account_payment_partner.field_res_users__customer_payment_mode_id
msgid "Select the default payment mode for this customer."
msgstr "Selecione o Modo de Pagamento do Cliente pré definido."

#. module: account_payment_partner
#: model:ir.model.fields,help:account_payment_partner.field_res_partner__supplier_payment_mode_id
#: model:ir.model.fields,help:account_payment_partner.field_res_users__supplier_payment_mode_id
msgid "Select the default payment mode for this supplier."
msgstr "Selecione o Modo de Pagamento do Fornecedor pré definido."

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_account_payment_mode__show_bank_account
msgid "Show Bank Account"
msgstr ""

#. module: account_payment_partner
#: model_terms:ir.ui.view,arch_db:account_payment_partner.account_payment_mode_form
msgid "Show bank account in invoice report"
msgstr "Mostrar conta bancária na fatura"

#. module: account_payment_partner
#: model:ir.model.fields,help:account_payment_partner.field_account_payment_mode__show_bank_account
msgid "Show in invoices partial or full bank account number"
msgstr "Mostrar em faturas a conta bancária completa ou parcial"

#. module: account_payment_partner
#: model:ir.model.fields,field_description:account_payment_partner.field_res_partner__supplier_payment_mode_id
#: model:ir.model.fields,field_description:account_payment_partner.field_res_users__supplier_payment_mode_id
msgid "Supplier Payment Mode"
msgstr "Modo de Pagamento do Fornecedor"

#. module: account_payment_partner
#: model:ir.model.fields,help:account_payment_partner.field_account_bank_statement_line__has_reconciled_items
#: model:ir.model.fields,help:account_payment_partner.field_account_move__has_reconciled_items
#: model:ir.model.fields,help:account_payment_partner.field_account_payment__has_reconciled_items
msgid "Technical field for supporting the editability of the payment mode"
msgstr ""

#. module: account_payment_partner
#: model:ir.model.fields,help:account_payment_partner.field_account_payment_mode__refund_payment_mode_id
msgid ""
"This payment mode will be used when doing refunds coming from the current "
"payment mode."
msgstr ""
"Este modo de pagamento será usado ao fazer reembolsos provenientes do modo "
"de pagamento atual."

#. module: account_payment_partner
#: code:addons/account_payment_partner/models/account_payment_mode.py:0
#, python-format
msgid ""
"You cannot change the Company. There exists at least one Journal Entry with "
"this Payment Mode, already assigned to another Company."
msgstr ""
"Você não pode mudar a Empresa. Existe pelo menos um Lançamento de Diário, "
"com este Modo de Pagamento, já atribuído a outra Empresa."

#. module: account_payment_partner
#: code:addons/account_payment_partner/models/account_payment_mode.py:0
#, python-format
msgid ""
"You cannot change the Company. There exists at least one Journal Item with "
"this Payment Mode, already assigned to another Company."
msgstr ""
"Não pode alterar a Empresa. Existe pelo menos um item de diáro com este Modo "
"de Pagamento já relacionado com outra Empresa."

#~ msgid "Display Name"
#~ msgstr "Nome a Exibir"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Última Modificação em"

#~ msgid "Show bank account"
#~ msgstr "Mostrar conta bancária"
