<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_suivi_labo_tree" model="ir.ui.view">
        <field name="name">suivi.labo.tree</field>
        <field name="model">suivi.labo</field>
        <field name="arch" type="xml">
            <tree string="Laboratory Follow-up">
                <field name="service_id" string ="Service"/>
                <field name="partner_id"/>
                <field name="order_id"/>
                <field name="date_reclamation"/>
                <field name="date_reception_labo"/>
                <field name="etat_avancement"/>
                <field name="etat_equipement"/>
                <field name="num_serie"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_suivi_labo_form" model="ir.ui.view">
        <field name="name">suivi.labo.form</field>
        <field name="model">suivi.labo</field>
        <field name="arch" type="xml">
            <form string="Laboratory Follow-up">
                <header>
                    <field name="etat_avancement" widget="statusbar" options="{'clickable': '1', 'fold_field': 'fold'}"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="service_id"/>
                            <field name="partner_id"/>
                            <field name="source_type" required="1"/>
                            <field name="complaint_id" attrs="{'invisible': [('source_type', '!=', 'complaint')], 'required': [('source_type', '=', 'complaint')]}"/>
                            <field name="visite_id" attrs="{'invisible': [('source_type', '!=', 'visite')], 'required': [('source_type', '=', 'visite')]}"/>
                            <field name="order_id"/>
                            <field name="post_installation_sending_date"/>
                            <field name="date_reclamation"/>
                            <field name="date_reception_labo"/>
                            <field name="representant"/>
                        </group>
                        <group>
                            <field name="date_estimation_sortie_labo"/>
                            <field name="date_sortie_labo"/>
                            <field name="etat_equipement"/>
                            <field name="valid_garantie"/>
                            <field name="num_serie"/>
                            <field name="natur_piece"/>
                            <field name="cause" attrs="{'invisible': [('show_cause', '=', False)], 'required': [('show_cause', '=', True)]}"/>
                            <field name="show_cause" invisible="1"/>
                            <field name="show_new_order" invisible="1"/>
                            <field name="new_order_id" attrs="{'invisible': [('show_new_order', '=', False)]}"/>
                            <field name="observation"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <group>
                                <field name="description_reclamation" nolabel="1" placeholder="Description de la réclamation..."/>
                            </group>
                        </page>
                        <page string="Intervention">
                            <group>
                                <field name="intervention_effectuee" nolabel="1"/>
                            </group>
                        </page>
                        <page string="Documents">
                            <group>
                                <field name="rapport_sav" filename="rapport_name"/>
                                <button name="action_preview_attachment" icon="fa-eye" string="Preview" type="object" class="btn-primary"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_suivi_labo_search" model="ir.ui.view">
        <field name="name">suivi.labo.search</field>
        <field name="model">suivi.labo</field>
        <field name="arch" type="xml">
            <search string="Search Laboratory Follow-up">
                <field name="service_id"/>
                <field name="partner_id"/>
                <field name="order_id"/>
                <field name="date_reclamation"/>
                <field name="date_reception_labo"/>
                <field name="num_serie"/>
                <field name="representant"/>
                <filter string="En Attente" name="en_attente" domain="[('etat_avancement', '=', 'en_attente')]"/>
                <filter string="En Cours" name="en_cours" domain="[('etat_avancement', '=', 'en_cours')]"/>
                <filter string="Terminé" name="termine" domain="[('etat_avancement', '=', 'termine')]"/>
                <group expand="0" string="Group By">
                    <filter string="Date de Réclamation" name="group_by_date_reclamation" context="{'group_by': 'date_reclamation'}"/>
                    <filter string="Partner" name="group_by_partner" context="{'group_by': 'partner_id'}"/>
                    <filter string="État d'avancement" name="group_by_etat" context="{'group_by': 'etat_avancement'}"/>
                    <filter string="État de l'Équipement" name="group_by_equipement" context="{'group_by': 'etat_equipement'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_suivi_labo" model="ir.actions.act_window">
        <field name="name">Suivi Labo</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">suivi.labo</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_suivi_labo_search"/>
    </record>

    <!-- Menu -->
    <menuitem id="menu_suivi_labo"
        name="Suivi Labo"
        parent="after_sale_service.menu_after_sale_service_root"
        action="action_suivi_labo"
        sequence="30"/>
</odoo>