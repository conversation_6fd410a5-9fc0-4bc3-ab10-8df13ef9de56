from odoo import models, fields
import logging

# Define the logger
_logger = logging.getLogger(__name__)

class AccountMoveLine(models.Model):
    _inherit = "account.move.line"

    payment_method_line_id = fields.Many2one('account.payment.method.line', string='Payment Method', related='payment_id.payment_method_line_id', store=True, index=True)
    
    def _prepare_statement_line_vals(self, statement):
        _logger.info("method %s",self.payment_method_line_id.id)
        self.ensure_one()
        amount = 0.0
        if self.debit > 0:
            amount = self.debit
        elif self.credit > 0:
            amount = -self.credit
        vals = {
            "name": self.num_piece or self.name or "?",
            "amount": amount,
            "partner_id": self.partner_id.id,
            "statement_id": statement.id,
            "payment_ref": self.ref,
            "date": statement.date,
            "foreign_currency_id": self.currency_id.id,
            "currency_id": statement.currency_id.id,
            "payment_method_line_id": self.payment_method_line_id.id,
            "date_maturity": self.date_maturity,
        }
        if statement.currency_id != self.currency_id:
            vals.update(
                {
                    "amount_currency": self.amount_currency,
                }
            )
        _logger.info("vals : %s",vals)
        return vals

    def create_statement_line_from_move_line(self, statement):
        abslo = self.env["account.bank.statement.line"]
        for mline in self:
            abslo.create(mline._prepare_statement_line_vals(statement))
        return
