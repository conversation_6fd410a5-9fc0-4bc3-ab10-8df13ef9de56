from odoo import api, fields, models

class TreasurySituationWizard(models.TransientModel):
    _name = 'treasury.situation.wizard'
    _description = 'Treasury Situation Generator Wizard'

    journal_ids = fields.Many2many(
        'account.journal',
        string='Bank Journals',
        domain=[
            ('type', '=', 'bank'),
        ],
        required=True,
    )

    bank_account_ids = fields.Many2many(
        'account.account',
        string='Bank Accounts',
        compute='_compute_bank_accounts',
        store=True,
    )

    @api.depends('journal_ids')
    def _compute_bank_accounts(self):
        for record in self:
            record.bank_account_ids = record.journal_ids.mapped('default_account_id')

    def action_generate_treasury_situation(self):
        self.ensure_one()
        treasury_situation = self.env['treasury.situation']
        
        # Clear existing records
        treasury_situation.search([]).unlink()
        
        # Generate treasury situation for selected accounts
        treasury_situation.with_context(
            selected_bank_accounts=self.bank_account_ids.ids,
            selected_journals=self.journal_ids.ids,
        ).Get_payment_Operations()
        
        # Return action to view the generated treasury situation
        action = self.env.ref('treasury_situation.action_treasury_situation').read()[0]
        return action 