<?xml version="1.0" encoding="utf-8" ?>
<odoo noupdate="1">
    <record id="account_payment_mode_company_rule" model="ir.rule">
        <field name="name">Payment mode multi-company rule</field>
        <field name="model_id" ref="model_account_payment_mode" />
        <field name="domain_force">
            ['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]
        </field>
    </record>
</odoo>
