from odoo import models, fields,api

class AfterSaleVisite(models.Model):
    _name = 'after.sale.visite'
    _description = 'After Sale Visite'

    service_id = fields.Many2one('after.sale.service', string='After Sale Service')
    partner_id = fields.Many2one('res.partner', related='service_id.partner_id', string='Partner', store=True)
    crm_id = fields.Many2one('crm.lead', string='CRM',domain="[('partner_id', '=', partner_id)]")
    counter_id = fields.Many2one('customer.reference', domain="[('partner_id', '=', partner_id)]")
    date_visite = fields.Date(string='Date Visite', compute='_compute_date_visite', store=True)
    user_id = fields.Many2one('res.users', string='User', default=lambda self: self.env.user,store=True)
    suivi_labo_ids = fields.One2many('suivi.labo', 'visite_id', string='Suivi Labo')
    
    @api.depends('detail_visite_ids', 'detail_visite_ids.date_visite')
    def _compute_date_visite(self):
        for rec in self:
            if rec.detail_visite_ids:
                # Sort detail_visite_ids by date_visite and take the first one
                sorted_details = rec.detail_visite_ids.sorted(key=lambda r: r.date_visite or fields.Date.today())
                rec.date_visite = sorted_details[0].date_visite
            else:
                rec.date_visite = False

    order_id = fields.Many2one('sale.order', string='Bon de Commande', domain="[('partner_id', '=', partner_id)]")
    installation_type = fields.Selection([
        ('', ''),
        ('after_sale', 'After-sales service circuit'),
        ('sale', 'Sales Circuit'),
        ('extension', 'Extension circuit'),
        ('installation', 'Installation circuit'),
        ('connected', 'Connected to Networks'),
        ('pumping_project', 'Pumping Project')
    ],compute="_compute_installation",inverse='set_all',store=True)
    puiss_equip = fields.Char(string='Puissance Equipement',compute='_compute_puiss_equip',inverse='set_all',store=True)
    puissance_installee = fields.Float (string='Puissance Installée',compute='_compute_installation',inverse='set_all',store=True)
    panneaux_installe = fields.Char(string='Panneaux Installés',compute='_compute_panneaux_installe',inverse='set_all',store=True)
    detail_visite_ids = fields.One2many('detail.visite', 'visite_id', string='Detail Visite')
    nb_panneaux = fields.Char(string='Nombre de Panneaux',compute='_compute_panneaux_installe',inverse='set_all',store=True)
    nb_coffret_ac = fields.Integer(string='Nombre de Coffret AC',compute='_compute_nb_coffret',inverse='set_all',store=True)
    nb_coffret_dc = fields.Integer(string='Nombre de Coffret DC',compute='_compute_nb_coffret',inverse='set_all',store=True)
    

    @api.depends('order_id')
    def _compute_installation(self):
        for rec in self:
            if rec.order_id:
                rec.installation_type = rec.order_id.installation_type
                rec.puissance_installee = rec.order_id.nb_kilo
                rec.user_id = rec.order_id.user_id
            else:
                rec.installation_type = ""
                rec.puissance_installee = 0


    @api.depends('order_id')
    def _compute_nb_coffret(self):
        for rec in self:
            nb_coffret_ac = 0
            nb_coffret_dc = 0
            if rec.order_id:
                for line in rec.order_id.order_line:
                    if 'coffret ac' in line.product_id.name.lower():
                        nb_coffret_ac += line.product_uom_qty
                    elif 'coffret dc' in line.product_id.name.lower():
                        nb_coffret_dc += line.product_uom_qty
            rec.nb_coffret_ac = nb_coffret_ac
            rec.nb_coffret_dc = nb_coffret_dc

    @api.depends('order_id')
    def _compute_panneaux_installe(self):
        for rec in self:
            rec.panneaux_installe = ""
            rec.nb_panneaux = ""
            if rec.order_id:
                for line in rec.order_id.order_line:
                    if line.product_id.categ_id.name == 'PANNEAUX':
                        rec.panneaux_installe = line.product_id.name
                        rec.nb_panneaux=line.product_uom_qty
                        break



    @api.depends('order_id','installation_type')
    def _compute_puiss_equip(self):
        for rec in self:
            rec.puiss_equip = ""
            if rec.order_id:
                if rec.installation_type == 'connected':
                    for line in rec.order_id.order_line:
                        if line.product_id.categ_id.name == 'ONDULEURS':
                            rec.puiss_equip = line.product_id.name
                            break
                elif rec.installation_type == 'pumping_project':
                    for line in rec.order_id.order_line:
                        if line.product_id.categ_id.name == 'VARIATEURS':
                            rec.puiss_equip = line.product_id.name
                            break
            else:
                rec.puiss_equip = ""

    def set_all(self):
        for rec in self:
            continue

