<?xml version="1.0" encoding="utf-8" ?>
<!--
Copyright 2021 Tecnativa - <PERSON><PERSON><PERSON>
License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).
-->
<odoo noupdate="1">
    <record id="view_account_invoice_report_search" model="ir.ui.view">
        <field name="name">account.invoice.report.search</field>
        <field name="model">account.invoice.report</field>
        <field name="inherit_id" ref="account.view_account_invoice_report_search" />
        <field name="arch" type="xml">
            <field name="partner_id" position="after">
                <field name="payment_mode_id" />
            </field>
            <filter name="category_product" position="after">
                <filter
                    string="Payment Mode"
                    name="payment_mode_id"
                    context="{'group_by': 'payment_mode_id'}"
                />
            </filter>
        </field>
    </record>
</odoo>
