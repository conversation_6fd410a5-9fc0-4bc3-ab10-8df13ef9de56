from odoo import fields, models, api, _
class AfterSaleComplaint(models.Model):

    _inherit = 'after.sale.complaint'

    partner_id = fields.Many2one('res.partner', related='service_id.partner_id', string='Partner', store=True)
    order_id = fields.Many2one('sale.order', string='Bon de Commande', domain="[('partner_id', '=', partner_id)]")
    user_id = fields.Many2one('res.users', string="Vendeur", required=True,store=True,default=lambda self: self.env.user)
    installation_type = fields.Selection([
        ('', ''),
        ('after_sale', 'After-sales service circuit'),
        ('sale', 'Sales Circuit'),
        ('extension', 'Extension circuit'),
        ('installation', 'Installation circuit'),
        ('connected', 'Connected to Networks'),
        ('pumping_project', 'Pumping Project')
    ],compute="_compute_installation",inverse='set_all',store=True)
    date_reclamation = fields.Date(string='Date de Réclamation')
    telephone = fields.Char(string='Téléphone Joignable')
    puissance_installee = fields.Float (string='Puissance Installée',compute='_compute_installation',inverse='set_all',store=True)
    nb_panneaux = fields.Char(string='Nombre de Panneaux',compute='_compute_nb_panneaux',inverse='set_all',store=True)
    puiss_equip = fields.Char(string='Puissance Equipement',compute='_compute_puiss_equip',inverse='set_all',store=True)
    date_reclamation = fields.Date(string='Date de Réclamation')
    post_installation_sending_date = fields.Date(string="Post Installation Sending Date", compute='_compute_post_installation_sending_date', store=True,inverse='set_all')
    date_reponse = fields.Date(string="Date de Réponse")
    temps_reponse = fields.Char(string="Temps de Réponse",compute='_compute_temps_reponse')
    traite_par = fields.Char(string="Traité par")
    description_reclamation = fields.Text(string="Description de la réclamation")
    intervention_effectuee = fields.Text(string="Intervention effectuée")
    etat_avancement = fields.Selection([('en_attente','En Attente'),('en_cours','En Cours'),('termine','Terminé'),('recommendation','Recommendation')],string="Etat d'avancement",default='en_attente')
    suivi_labo_ids = fields.One2many('suivi.labo', 'complaint_id', string='Suivi Labo')
    rapport_reclamation = fields.Binary(string="Rapport Réclamation")
    photos_reclamation_ids = fields.Many2many(
        'ir.attachment',
        'after_sale_complaint_reclamation_rel',
        'complaint_id', 'attachment_id',
        string="Photos Réclamation",
        domain=[('mimetype', 'ilike', 'image')],
    )

    photos_apres_intervention_ids = fields.Many2many(
        'ir.attachment',
        'after_sale_complaint_apres_intervention_rel',
        'complaint_id', 'attachment_id',
        string="Photos Après Intervention",
        domain=[('mimetype', 'ilike', 'image')],
    )
    retour_telep = fields.Selection([('oui','Oui'),('non','Non')],string="Retour Téléphonique")
    date_retour = fields.Date(string="Date de Retour Téléphonique")
    recommendation = fields.Text(string="Recommendation")
    @api.depends('order_id')
    def _compute_installation(self):
        for rec in self:
            if rec.order_id:
                rec.installation_type = rec.order_id.installation_type
                rec.puissance_installee = rec.order_id.nb_kilo
                rec.user_id = rec.order_id.user_id.id
            else:
                rec.installation_type = ""
                rec.puissance_installee = 0
                
    @api.depends('date_reponse','post_installation_sending_date')
    def _compute_temps_reponse(self):
        for complaint in self:
            if complaint.date_reponse and complaint.post_installation_sending_date:
                complaint.temps_reponse = complaint.date_reponse - complaint.post_installation_sending_date
            else:
                complaint.temps_reponse = False


    
    @api.depends('order_id.post_installation_id.sending_date')
    def _compute_post_installation_sending_date(self):
        for complaint in self:
            if complaint.order_id and complaint.order_id.post_installation_id:
                complaint.post_installation_sending_date = complaint.order_id.post_installation_id.sending_date
            else:
                complaint.post_installation_sending_date = False

    @api.depends('order_id','installation_type')
    def _compute_puiss_equip(self):
        for rec in self:
            rec.puiss_equip = ""
            if rec.order_id:
                if rec.installation_type == 'connected':
                    for line in rec.order_id.order_line:
                        if line.product_id.categ_id.name == 'ONDULEURS':
                            rec.puiss_equip = line.product_id.name
                            break
                elif rec.installation_type == 'pumping_project':
                    for line in rec.order_id.order_line:
                        if line.product_id.categ_id.name == 'VARIATEURS':
                            rec.puiss_equip = line.product_id.name
                            break
            else:
                rec.puiss_equip = ""

    
    
    @api.depends('order_id')
    def _compute_nb_panneaux(self):
        for rec in self:
            if rec.order_id:
                nb_panneaux = 0
                for line in rec.order_id.order_line:
                    if line.product_id.categ_id.name == 'PANNEAUX':
                        nb_panneaux += line.product_uom_qty
                rec.nb_panneaux = nb_panneaux
            else:
                rec.nb_panneaux = 0

    

    def set_all(self):
        for rec in self:
            continue