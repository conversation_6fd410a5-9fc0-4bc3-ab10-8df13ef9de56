# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment_order
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2021-04-15 17:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"%(count)d payment lines added to the existing draft payment order <a href=# "
"data-oe-model=account.payment.order data-oe-id=%(order_id)d>%(name)s</a>."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"%(count)d payment lines added to the new draft payment order <a href=# data-"
"oe-model=account.payment.order data-oe-id=%(order_id)d>%(name)s</a>, which "
"has been automatically created."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "<b>Account Number</b>: %(number)s - <b>Partner</b>: %(name)s"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Company Currency:</strong>"
msgstr "<strong>Devise société:</strong>"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Execution:</strong>"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Payment Type:</strong>"
msgstr "<strong>Type de Paiement:</strong>"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Reference</strong>"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Total</strong>"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Used Account:</strong>"
msgstr "<strong>Compte utilisé:</strong>"

#. module: account_payment_order
#: model:ir.model.constraint,message:account_payment_order.constraint_account_payment_line_name_company_unique
msgid "A payment line already exists with this reference in the same company!"
msgstr ""
"Une ligne de paiement existe déjà avec cette référence dans la même société !"

#. module: account_payment_order
#: code:addons/account_payment_order/models/res_bank.py:0
#, python-format
msgid ""
"A valid BIC contains 8 or 11 characters. The BIC '%(bic)s' contains %(num)d "
"characters, so it is not valid."
msgstr ""

#. module: account_payment_order
#: model:res.groups,name:account_payment_order.group_account_payment
msgid "Accounting / Payments"
msgstr "Comptabilité / Paiements"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_line__bank_account_required
msgid ""
"Activate this option if this payment method requires you to know the bank "
"account number of your customer or supplier."
msgstr ""
"Activez cette option si cette méthode de paiement vous demande de connaître "
"le numéro de compte bancaire de votre client ou fournisseur."

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_ids
msgid "Activities"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_state
msgid "Activity State"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Add All Move Lines"
msgstr "Ajouter des écritures comptables"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_move_form
msgid "Add to Debit Order"
msgstr "Ajouter à l'ordre de débit"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_move_form
msgid "Add to Payment Order"
msgstr "Ajouter à l'ordre de paiement"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_invoice_create_account_payment_line_action
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_invoice_tree
msgid "Add to Payment/Debit Order"
msgstr "Ajouter à l'ordre de paiement/débit"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__target_move__all
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_target_move__all
msgid "All Entries"
msgstr "Toutes les écritures"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__target_move__posted
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_target_move__posted
msgid "All Posted Entries"
msgstr "Toutes les écritures passées"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__allow_blocked
msgid "Allow Litigation Move Lines"
msgstr "Autoriser les lignes d'écriture en litige"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__allowed_journal_ids
msgid "Allowed journals"
msgstr "Journaux autorisés"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__amount_currency
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_tree
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Amount"
msgstr "Montant"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__amount_company_currency
msgid "Amount in Company Currency"
msgstr "Montant dans la devise société"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__payment_mode__any
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_payment_mode__any
msgid "Any"
msgstr "Tout"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_attachment_count
msgid "Attachment Count"
msgstr "Nbre de pièces jointes"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "Attachments"
msgstr "Pièces jointes"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Back to Draft"
msgstr "Remettre en brouillon"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_res_bank
msgid "Bank"
msgstr "Banque"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Bank Account"
msgstr "Compte bancaire"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__bank_account_required
msgid "Bank Account Required"
msgstr "Compte bancaire requis"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__journal_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Bank Journal"
msgstr "Journal de banque"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_move_line__partner_bank_id
msgid "Bank account on which we should pay the supplier"
msgstr "Compte bancaire sur lequel nous devrions payer le fournisseur"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__cancel
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Cancel"
msgstr "Annuler"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Cancel Payments"
msgstr "Paiements annulés"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Choose Move Lines Filter Options"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid ""
"Click on Add All Move Lines to auto-select the move lines matching the above "
"criteria or click on Add an item to manually select the move lines filtered "
"by the above criteria."
msgstr ""
"Cliquez sur Ajouter toutes les lignes d'écriture pour sélectionner "
"automatiquement les lignes correspondant aux critères ci-dessus ou cliquez "
"sur Ajouter un élément pour sélectionner manuellement les lignes d'écriture "
"filtrées par les critères ci-dessus."

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__communication
msgid "Communication"
msgstr "Communication"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__communication_type
msgid "Communication Type"
msgstr "Type de communication"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_line.py:0
#, python-format
msgid "Communication is empty on payment line %s."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__company_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__company_id
msgid "Company"
msgstr "Société"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__company_partner_bank_id
msgid "Company Bank Account"
msgstr "Compte bancaire de la société"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Confirm Payments"
msgstr "Confirmer le paiement"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__open
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Confirmed"
msgstr "Confirmé"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "Create"
msgstr "Créer"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "Create Payment Lines"
msgstr "Créer les lignes de paiement"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Create Payment Lines from Journal Items"
msgstr "Créer les lignes de paiement à partir de la pièce comptable"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Create Transactions"
msgstr "Créer les opérations"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_line_create_action
msgid "Create Transactions from Move Lines"
msgstr "Créer les opérations à partir des écritures comptables"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_invoice_payment_line_multi
msgid "Create payment lines from invoice tree view"
msgstr "Créer les lignes de paiement à partir de la liste des factures"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__create_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__create_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__create_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__create_uid
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "Created by"
msgstr "Créée par"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__create_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__create_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__create_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__create_date
msgid "Created on"
msgstr "Créée le"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__company_currency_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__company_currency_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Currency"
msgstr "Devise"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__currency_id
msgid "Currency of the Payment Transaction"
msgstr "Devise de l'opération de paiement"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Debit Order"
msgstr "Ordre de prélèvement"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_order_inbound_action
#: model:ir.ui.menu,name:account_payment_order.account_payment_order_inbound_menu
msgid "Debit Orders"
msgstr "Ordres de prélèvement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_date_prefered
msgid "Default Payment Execution Date"
msgstr "Date d'exécution du paiement par défaut"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__description
msgid "Description"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__no_debit_before_maturity
msgid "Disallow Debit Before Maturity Date"
msgstr "Interdire le prélèvement avant la date d'échéance"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__display_name
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__display_name
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__display_name
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__display_name
msgid "Display Name"
msgstr "Nom à afficher"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__draft
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Draft"
msgstr "Brouillon"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_type__due
msgid "Due"
msgstr "Échéance"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__ml_maturity_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__due_date
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__date_type__due
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_prefered__due
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__date_prefered__due
msgid "Due Date"
msgstr "Date d'échéance"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__generated
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Generated"
msgstr "Fichier généré"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_generated
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Generation Date"
msgstr "Date de la génération du fichier"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "File Successfully Uploaded"
msgstr "Fichier téléchargé avec succès"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_uploaded
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Upload Date"
msgstr "Date de téléchargement du fichier"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__uploaded
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Uploaded"
msgstr "Fichier téléchargé"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_prefered__fixed
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__date_prefered__fixed
msgid "Fixed Date"
msgstr "Date fixe"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__bank_account_link
msgid ""
"For payment modes that are always attached to the same bank account of your "
"company (such as wire transfer from customers or SEPA direct debit from "
"suppliers), select 'Fixed'. For payment modes that are not always attached "
"to the same bank account (such as SEPA Direct debit for customers, wire "
"transfer to suppliers), you should select 'Variable', which means that you "
"will select the bank account on the payment order. If your company only has "
"one bank account, you should always select 'Fixed'."
msgstr ""
"Pour les modes de paiement qui sont toujours rattachés au même compte "
"bancaire de votre société (par exemple, le transfert de clients ou le "
"prélèvement SEPA auprès de fournisseurs), sélectionnez 'Fixe'. Pour les "
"modes de paiement qui ne sont pas toujours rattachés au même compte bancaire "
"(comme le prélèvement SEPA direct pour les clients, le transfert bancaire "
"aux fournisseurs), vous devez choisir 'Variable', ce qui signifie que vous "
"sélectionnez le compte bancaire sur l'ordre de paiement. Si votre entreprise "
"ne dispose que d'un compte bancaire, vous devez toujours sélectionner 'Fixe'."

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line__communication_type__normal
msgid "Free"
msgstr "Libre"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_move__reference_type__none
msgid "Free Reference"
msgstr "Référence libre"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Generate Payment File"
msgstr "Générer un fichier de paiement"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "Generated File"
msgstr "Fichier généré"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__generated_user_id
msgid "Generated by"
msgstr "Généré par"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Group By"
msgstr "Regrouper par"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__group_lines
msgid "Group Transactions in Payment Orders"
msgstr "Grouper les opérations des ordres de paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__has_message
msgid "Has Message"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__id
msgid "ID"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_needaction
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_mode__group_lines
msgid ""
"If this mark is checked, the transaction lines of the payment order will be "
"grouped upon confirmation of the payment order.The grouping will be done "
"only if the following fields matches:\n"
"* Partner\n"
"* Currency\n"
"* Destination Bank Account\n"
"* Payment Date\n"
"and if the 'Communication Type' is 'Free'\n"
"(other modules can set additional fields to restrict the grouping.)"
msgstr ""
"Si coché, les lignes de l'opération de l'ordre de paiement seront groupées "
"lors de la confirmation de l'ordre de paiement. Le regroupement ne sera "
"effectué que si les champs suivants correspondent :\n"
"Partenaire\n"
"* Devise\n"
"* Compte bancaire de destination\n"
"* Date de paiement\n"
"Et si le 'Type de communication' est 'Libre'\n"
"(D'autres modules peuvent définir des champs supplémentaires pour "
"restreindre le regroupement.)"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_mode__no_debit_before_maturity
msgid ""
"If you activate this option on an Inbound payment mode, you will have an "
"error message when you confirm a debit order that has a payment line with a "
"payment date before the maturity date."
msgstr ""
"Si vous activez cette option sur un mode de paiement entrant, un message "
"d'erreur s'affichera lorsque vous confirmez un ordre de prélèvement "
"comportant une ligne de paiement dont la date de paiement est antérieure à "
"la date d'échéance."

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_prefered__now
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__date_prefered__now
msgid "Immediately"
msgstr "Immédiatement"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__payment_type__inbound
msgid "Inbound"
msgstr "Entrant"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_journal__inbound_payment_order_only
msgid "Inbound Payment Order Only"
msgstr "Ordre de paiement entrant uniquement"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Invoice Ref"
msgstr "Ref Facture"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_is_follower
msgid "Is Follower"
msgstr "Est abonné"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_journal
msgid "Journal"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__move_ids
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Journal Entries"
msgstr "Pièces comptables"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_move
msgid "Journal Entry"
msgstr "Ecriture"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_move_line
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__move_line_id
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__journal_ids
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_journal_ids
msgid "Journals Filter"
msgstr "Filtre sur les journaux"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Keep empty for using all journals"
msgstr "Garder vide pour utiliser tous les journaux"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Keep empty to use all partners"
msgstr "Garder vide pour utiliser tous les partenaires"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_line__communication
msgid "Label of the payment that will be seen by the destinee"
msgstr "Libellé du paiement qui sera vu par le destinataire"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi____last_update
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line____last_update
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create____last_update
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__write_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__write_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__write_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__write_uid
msgid "Last Updated by"
msgstr "Dernière modification par"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__write_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__write_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__write_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__write_date
msgid "Last Updated on"
msgstr "Modifié le"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__bank_account_link
msgid "Link to Bank Account"
msgstr "Lien vers le compte bancaire"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__invoice
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_invoice
msgid "Linked to an Invoice or Refund"
msgstr "Lié à une facture ou à un avoir"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pièce jointe principale"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_ids
msgid "Messages"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "Missing Bank Journal on payment order %s."
msgstr "Journal bancaire manquant sur l'ordre de paiement %s."

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_line.py:0
#, python-format
msgid "Missing Partner Bank Account on payment line %s"
msgstr "Compte bancaire du partenaire manquant sur la ligne de paiement %s"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "Missing bank account on bank journal '%s'."
msgstr "Journal bancaire manquant sur le journal '%s'."

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_type__move
msgid "Move"
msgstr "Écriture"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__move_date
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__date_type__move
msgid "Move Date"
msgstr "Date du mouvement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__move_line_ids
msgid "Move Lines"
msgstr "Écritures comptables"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Name or Description"
msgstr "Nom ou description"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"No Payment Line created for invoice %s because its payment mode is not "
"intended for payment orders."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid "No Payment Mode on invoice %s"
msgstr "Pas de mode de paiement sur la facture %s"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid "No pending AR/AP lines to add on %s"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__name
msgid "Number"
msgstr "Numéro"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__move_count
msgid "Number of Journal Entries"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_count
msgid "Number of Payment Transactions"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Nombre de messages non lus"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__old_bank_payment_line_name
msgid "Old Bank Payment Line Name"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"On payment order %(porder)s, the Payment Execution Date is in the past "
"(%(exedate)s)."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_method__payment_order_only
msgid "Only for payment orders"
msgstr "Uniquement pour les ordres de paiement"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_mode_form
msgid "Options for Payment Orders"
msgstr "Options pour les ordres de paiement"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__payment_type__outbound
msgid "Outbound"
msgstr "Sortant"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_journal__outbound_payment_order_only
msgid "Outbound Payment Order Only"
msgstr "Ordre de paiement sortant uniquement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Partner"
msgstr "Partenaire"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_move_line__partner_bank_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__partner_bank_id
msgid "Partner Bank Account"
msgstr "Compte bancaire partenaire"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__partner_banks_archive_msg
msgid "Partner Banks Archive Msg"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__partner_ids
msgid "Partners"
msgstr "Partenaires"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__date
msgid "Payment Date"
msgstr "Date de paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_scheduled
msgid "Payment Execution Date"
msgstr "Date d'exécution du paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_prefered
msgid "Payment Execution Date Type"
msgstr "Type de la date d'exécution du paiement"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "Payment File"
msgstr "Fichier de paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_line_ids
msgid "Payment Line"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_line_date
msgid "Payment Line Date"
msgstr ""

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_line_action
#: model:ir.model,name:account_payment_order.model_account_payment_line
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_form
msgid "Payment Lines"
msgstr "Lignes de paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_method_id
msgid "Payment Method"
msgstr "Méthode de paiement"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_method
msgid "Payment Methods"
msgstr "Méthodes de paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__payment_mode
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_mode_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Payment Mode"
msgstr "Mode de paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_payment_mode
msgid "Payment Mode on Invoice"
msgstr "Mode de paiement sur la facture"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_mode
msgid "Payment Modes"
msgstr "Modes de paiement"

#. module: account_payment_order
#: model:ir.actions.report,name:account_payment_order.action_print_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_bank_statement_line__payment_order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_move__payment_order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__order_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Payment Order"
msgstr "Ordre de paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_bank_statement_line__payment_order_ok
#: model:ir.model.fields,field_description:account_payment_order.field_account_move__payment_order_ok
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_order_ok
msgid "Payment Order Ok"
msgstr "Ordre de paiement OK"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_order_outbound_action
#: model:ir.ui.menu,name:account_payment_order.account_payment_order_outbound_menu
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_graph
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_pivot
msgid "Payment Orders"
msgstr "Ordre de paiements"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__name
msgid "Payment Reference"
msgstr "Référence du paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_ids
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_tree
msgid "Payment Transactions"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__payment_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_type
msgid "Payment Type"
msgstr "Type de paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_move_line__payment_line_ids
msgid "Payment lines"
msgstr "Lignes de paiement"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__payment_ids
msgid "Payment transaction"
msgstr ""

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_form
msgid "Payments"
msgstr "Paiements"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_bank_statement_line__reference_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_move__reference_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__reference_type
msgid "Reference Type"
msgstr "Type de Référence"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__payment_mode__same
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_payment_mode__same
msgid "Same"
msgstr "Identique"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__payment_mode__same_or_null
msgid "Same or Empty"
msgstr "Identique ou vide"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_payment_mode__same_or_null
msgid "Same or empty"
msgstr "Identique ou vide"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Search Payment Orders"
msgstr "Ordres de paiement"

#. module: account_payment_order
#: code:addons/account_payment_order/wizard/account_payment_line_create.py:0
#, python-format
msgid "Select Move Lines to Create Transactions"
msgstr "Sélectionner les écritures comptables pour créer les opérations"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_mode_form
msgid "Select Move Lines to Pay - Default Values"
msgstr "Sélectionner les lignes d'écriture à payer - Valeurs par défaut"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__date_scheduled
msgid ""
"Select a requested date of execution if you selected 'Due Date' as the "
"Payment Execution Date Type."
msgstr ""
"Sélectionner une date d'exécution requise si vous avez sélectionné 'Date "
"d'échéance' comme type de date d'exécution de paiement."

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__payment_order_ok
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_mode_search
msgid "Selectable in Payment Orders"
msgstr "Ordres de paiement disponibles"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Selected Move Lines to Create Transactions"
msgstr "Sélectionner les écritures comptables pour créer les opérations"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__state
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "State"
msgstr "État"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__state
msgid "Status"
msgstr "Statut"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_move__reference_type__structured
msgid "Structured Reference"
msgstr "Référence structurée"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__target_move
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_target_move
msgid "Target Moves"
msgstr "Mouvements cibles"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_report_account_payment_order_print_account_payment_order_main
msgid "Technical model for printing payment order"
msgstr "Modèle pour l'impression de l'ordre de paiement"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"The amount for Partner '%(partner)s' is negative or null (%(amount).2f) !"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "The following bank accounts are archived:"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"The invoice %(move)s is already added in the payment order(s) %(order)s."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid "The invoice %s is not in Posted state"
msgstr "La facture %s n'est pas validée"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"The payment mode '%(pmode)s' has the option 'Disallow Debit Before Maturity "
"Date'. The payment line %(pline)s has a maturity date %(mdate)s which is "
"after the computed payment date %(pdate)s."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"The payment type (%(ptype)s) is not the same as the payment type of the "
"payment mode (%(pmode)s)"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "There are no transactions on payment order %s."
msgstr "Il n'y a aucune opération sur l'ordre de paiement %s."

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "There's at least one validation error:\n"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_line__ml_maturity_date
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""
"Ce champ est utilisé pour les écritures de journal payables et recevables. "
"Vous pouvez mettre la date limite pour le paiement de cette ligne."

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_method__payment_order_only
msgid ""
"This option helps enforcing the use of payment orders for some payment "
"methods."
msgstr ""
"Cette option permet d'imposer l'utilisation d'ordres de paiement pour "
"certains modes de paiement."

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "This wizard will create payment lines for the selected invoices:"
msgstr ""
"Cet assistant créera des lignes de paiement pour les factures sélectionnées :"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Total (Currency)"
msgstr "Total (en devise)"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__total_company_currency
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_tree
msgid "Total Company Currency"
msgstr "Total dans la devise de la société"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Total Residual"
msgstr "Total résiduel"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_tree
msgid "Total in Company Currency"
msgstr "Total dans la devise de la société"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_line_ids
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Transactions"
msgstr "Opérations"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__date_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_date_type
msgid "Type of Date Filter"
msgstr "Type du filtre sur la date"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_unread
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Nbre de messages non lus"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Value Date"
msgstr "Date de valeur"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_line_create
msgid "Wizard to create payment lines"
msgstr "Assistant pour créer des lignes de paiement"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"You cannot delete an uploaded payment order. You can cancel it in order to "
"do so."
msgstr ""
"Impossible de supprimer un ordre de paiement mis en ligne. Vous pouvez "
"l'annuler dans le but de faire cela."

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid ""
"if there are existing draft payment orders for the payment modes of the "
"invoices, the payment lines will be added to those payment orders"
msgstr ""
"s'il existe des ordres de paiement existants pour les modes de paiement des "
"factures, les lignes de paiement seront ajoutées à ces ordres de paiement"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "on"
msgstr "sur"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "otherwise, new payment orders will be created (one per payment mode)."
msgstr ""
"sinon, de nouveaux ordres de paiement seront créés (un par mode de paiement)."

#, python-format
#~ msgid ""
#~ "No handler for this payment method. Maybe you haven't installed the "
#~ "related Odoo module."
#~ msgstr ""
#~ "Aucun gestionnaire pour ce mode de paiement. Vous n'avez peut-être pas "
#~ "installé le module Odoo associé."

#, python-format
#~ msgid ""
#~ "No Payment Line created for invoice %s because it already exists or "
#~ "because this invoice is already paid."
#~ msgstr ""
#~ "Aucune ligne de paiement créée pour la facture %s parce qu'elle existe "
#~ "déjà ou parce que cette facture est déjà payée."

#~ msgid "Accounting Entries Options"
#~ msgstr "Options des pièces comptables"

#~ msgid "Bank Payment Line"
#~ msgstr "Ligne de paiement bancaire"

#~ msgid "Bank Payment Line Ref"
#~ msgstr "Référence de la ligne de paiement bancaire"

#~ msgid "Bank Payment Lines"
#~ msgstr "Lignes de paiement bancaires"

#, python-format
#~ msgid "Debit bank line %s"
#~ msgstr "Prélèvement ligne bancaire %s"

#, python-format
#~ msgid "Debit order %s"
#~ msgstr "Ordre de prélèvement %s"

#~ msgid "Generate Accounting Entries On File Upload"
#~ msgstr "Générer des écritures comptables lors du téléchargement de fichier"

#~ msgid "Move Option"
#~ msgstr "Option écriture"

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', you must choose an option for the 'Move Option' "
#~ "parameter."
#~ msgstr ""
#~ "Sur le mode de paiement '%s', vous devez choisir une option pour le "
#~ "paramètre 'Option d'écriture'."

#~ msgid "One move per payment date"
#~ msgstr "Un mouvement par date de paiement"

#~ msgid "One move per payment line"
#~ msgstr "Un mouvement par ligne de paiement"

#~ msgid "Order"
#~ msgstr "Ordre"

#, python-format
#~ msgid "Payment bank line %s"
#~ msgstr "Ligne bancaire du paiement %s"

#, python-format
#~ msgid "Payment order %s"
#~ msgstr "Ordre de paiement %s"

#~ msgid "Post Move"
#~ msgstr "Mouvement posté"

#~ msgid "Related Payment Lines"
#~ msgstr "Lignes de paiement liés"

#~ msgid "Search Bank Payment Lines"
#~ msgstr "Lignes de paiement bancaires"

#~ msgid ""
#~ "The bank payment lines are used to generate the payment file. They are "
#~ "automatically created from transaction lines upon confirmation of the "
#~ "payment order: one bank payment line can group several transaction lines "
#~ "if the option 'Group Transactions in Payment Orders' is active on the "
#~ "payment mode."
#~ msgstr ""
#~ "Les lignes de paiement bancaires servent à générer le fichier de "
#~ "paiement. Ils sont créés automatiquement à partir des lignes de "
#~ "transaction lors de la confirmation de l'ordre de paiement: une ligne de "
#~ "paiement bancaire peut regrouper plusieurs lignes de transaction si "
#~ "l'option 'Opérations de groupe dans les ordres de paiement' est active "
#~ "sur le mode de paiement."

#~ msgid "Total Amount"
#~ msgstr "Montant total"

#~ msgid "%d payment lines added to the existing draft payment order %s."
#~ msgstr ""
#~ "Lignes de paiement %d ont été ajouté à l'ordre de paiement %s en "
#~ "brouillon."

#~ msgid ""
#~ "%d payment lines added to the new draft payment order %s which has been "
#~ "automatically created."
#~ msgstr ""
#~ "Lignes de paiement %d ont été ajouté au nouveau ordre de paiement %s qui "
#~ "a été automatiquement créé."

#~ msgid ""
#~ "A valid BIC contains 8 or 11 characters. The BIC '%s' contains %d "
#~ "characters, so it is not valid."
#~ msgstr ""
#~ "Un BIC valide contient 8 ou 11 caractères. Le BIC '%s' contient %d "
#~ "caractères, donc il n'est pas valide."

#~ msgid "Can not reconcile: no move line for payment line %s of partner '%s'."
#~ msgstr ""
#~ "Impossible de rapprocher : aucune ligne d'écriture pour la ligne de "
#~ "paiement %s du partenaire '%s'."

#~ msgid ""
#~ "Cannot delete a payment order line whose payment order is in state '%s'. "
#~ "You need to cancel it first."
#~ msgstr ""
#~ "Impossible de supprimer une ligne d'ordre de paiement tant que l'ordre de "
#~ "paiement est dans l'état '%s'. Vous devez tout d'abord l'annuler."

#~ msgid ""
#~ "For partner '%s', the account of the account move line to pay (%s) is "
#~ "different from the account of of the transit move line (%s)."
#~ msgstr ""
#~ "Pour le partenaire '%s', le compte de la ligne d'écriture à payer (%s) "
#~ "est différent du compte de la ligne d'écriture du transfert (%s)."

#~ msgid "Move line '%s' of partner '%s' has already been reconciled"
#~ msgstr "La ligne d'écriture '%s' du partenaire '%s' a déjà été lettrée"

#~ msgid "On payment order %s, the Payment Execution Date is in the past (%s)."
#~ msgstr ""
#~ "Sur l'ordre de paiement %s, la date d'exécution du paiement est passée "
#~ "(%s)."

#~ msgid "The amount for Partner '%s' is negative or null (%.2f) !"
#~ msgstr "Le montant pour le partenaire '%s' est négatif ou nul (%.2f) !"

#~ msgid ""
#~ "The payment mode '%s' has the option 'Disallow Debit Before Maturity "
#~ "Date'. The payment line %s has a maturity date %s which is after the "
#~ "computed payment date %s."
#~ msgstr ""
#~ "Le mode de paiement '%s' a l'option 'Ne pas débiter avant la date "
#~ "d'échéance'. La ligne de paiement %s a une date d'échéance %s qui est "
#~ "postérieure à la date de paiement calculée %s."

#~ msgid ""
#~ "The payment type (%s) is not the same as the payment type of the payment "
#~ "mode (%s)"
#~ msgstr ""
#~ "Le type de paiement (%s) n'est pas le même que le type de paiement du "
#~ "mode de paiement (%s)"

#~ msgid "Done"
#~ msgstr "Fait"

#~ msgid "Done Date"
#~ msgstr "Fait le"

#~ msgid ""
#~ "Journal to write payment entries when confirming payment/debit orders of "
#~ "this mode"
#~ msgstr ""
#~ "Journal pour écrire les entrées de paiement lors de la confirmation des "
#~ "ordres de paiement / débit de ce mode"

#~ msgid "Number of Bank Lines"
#~ msgstr "Nombre de lignes bancaires"

#~ msgid "Offsetting Account"
#~ msgstr "Compte de compensation"

#~ msgid ""
#~ "On the payment mode '%s', you must select a value for the 'Transfer "
#~ "Account'."
#~ msgstr ""
#~ "Sur le mode de paiement '%s', vous devez sélectionner une valeur pour le "
#~ "'Compte de transfert'."

#~ msgid ""
#~ "On the payment mode '%s', you must select a value for the 'Transfer "
#~ "Journal'."
#~ msgstr ""
#~ "Sur le mode de paiement '%s', vous devez sélectionner une valeur pour le "
#~ "'Journal de transfert'."

#~ msgid ""
#~ "On the payment mode '%s', you must select an option for the 'Offsetting "
#~ "Account' parameter"
#~ msgstr ""
#~ "Sur le mode de paiement '%s', vous devez sélectionner une option pour le "
#~ "paramètre 'Compte de compensation'"

#~ msgid ""
#~ "Pay off lines in 'file uploaded' payment orders with a move on this "
#~ "account. You can only select accounts that are marked for reconciliation"
#~ msgstr ""
#~ "Payer les lignes du 'fichier télécharché' des ordres de paiement avec une "
#~ "écriture sur ce compte. Vous ne pouvez sélectionner que les comptes "
#~ "marqués pour le rapprochement"

#~ msgid "Transaction Lines"
#~ msgstr "Lignes de l'opération"

#~ msgid "Transfer Account"
#~ msgstr "Compte de transfert"

#~ msgid "Transfer Journal"
#~ msgstr "Journal d'opération"

#~ msgid "Transfer Journal Entries"
#~ msgstr "Pièce comptable de l'opération"
