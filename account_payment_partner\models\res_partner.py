# Copyright 2014 Akretion - <PERSON> <<EMAIL>>
# Copyright 2014 Tecnativa - Pedro <PERSON>ez<PERSON>
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html).

from odoo import api, fields, models


class ResPartner(models.Model):
    _inherit = "res.partner"

    supplier_payment_mode_id = fields.Many2one(
        comodel_name="account.payment.mode",
        company_dependent=True,
        check_company=True,
        domain="[('payment_type', '=', 'outbound'),"
        "('company_id', '=', current_company_id)]",
        help="Select the default payment mode for this supplier.",
    )
    customer_payment_mode_id = fields.Many2one(
        comodel_name="account.payment.mode",
        company_dependent=True,
        check_company=True,
        domain="[('payment_type', '=', 'inbound'),"
        "('company_id', '=', current_company_id)]",
        help="Select the default payment mode for this customer.",
    )

    @api.model
    def _commercial_fields(self):
        res = super()._commercial_fields()
        res += ["supplier_payment_mode_id", "customer_payment_mode_id"]
        return res
