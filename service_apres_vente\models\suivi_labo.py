from odoo import fields, models, api,_  
from odoo.exceptions import UserError
import logging
class SuiviLabo(models.Model):
    _name = 'suivi.labo'
    _description = 'Suivi Labo'

    service_id = fields.Many2one('after.sale.service', string='Service')
    partner_id = fields.Many2one('res.partner', related='service_id.partner_id', string='Partner', store=True)
    order_id = fields.Many2one('sale.order', string='Bon de Commande', domain="[('partner_id', '=', partner_id)]")
    source_type = fields.Selection([
        ('complaint', 'Réclamation'),
        ('visite', 'Visite')
    ], string="Source")
    complaint_id = fields.Many2one('after.sale.complaint', string='Complaint', 
        domain="[('service_id', '=', service_id)]")
    visite_id = fields.Many2one('after.sale.visite', string='Visite', 
        domain="[('service_id', '=', service_id)]")

    @api.onchange('source_type')
    def _onchange_source_type(self):
        self.complaint_id = False
        self.visite_id = False

    @api.onchange('complaint_id', 'visite_id')
    def _onchange_source(self):
        if self.complaint_id:
            self.date_reclamation = self.complaint_id.date_reclamation
        elif self.visite_id:
            self.date_reclamation = self.visite_id.date_visite

    """ @api.constrains('source_type', 'complaint_id', 'visite_id')
    def _check_source(self):
        for record in self:
            if record.source_type == 'complaint' and record.visite_id:
                raise ValidationError(_('You cannot select a visite when source type is complaint'))
            if record.source_type == 'visite' and record.complaint_id:
                raise ValidationError(_('You cannot select a complaint when source type is visite'))
            if record.source_type == 'complaint' and not record.complaint_id:
                raise ValidationError(_('Please select a complaint'))
            if record.source_type == 'visite' and not record.visite_id:
                raise ValidationError(_('Please select a visite')) """

    post_installation_sending_date = fields.Date(string="Post Installation Sending Date", compute='_compute_post_installation_sending_date', store=True,inverse='set_all')
    date_reclamation = fields.Date(string="Date de Reclamation")
    date_reception_labo = fields.Date(string="Date de Reception au Labo")
    description_reclamation = fields.Text(string="Description de la Reclamation")
    representant = fields.Many2one('res.users', string="Représentant")
    intervention_effectuee = fields.Char(string="Intervention effectuée")
    etat_avancement = fields.Selection([('en_cours','Demande En Cours'),('trait_liv','Demande Traitée + Article Livrée'),('trait_n_liv','Demande Traitée + Article Non Livrée'),('n_tr_liv','Demande Non Traitée + Article Livrée')],string="Etat d'avancement",default='en_cours')
    date_estimation_sortie_labo = fields.Date(string="Date d'estimation de sortie du Labo")
    date_sortie_labo = fields.Date(string="Date de sortie du Labo")
    etat_equipement = fields.Selection([('non','Non Réparable'),('reparable','Reparable')],string="Etat de l'Equipement")
    rapport_sav = fields.Binary(string="Rapport SAV")
    valid_garantie = fields.Selection([('oui','En Garantie'),('non','Hors Garantie')],string="Validité de la garantie")
    show_cause = fields.Boolean(compute='_compute_show_cause', store=False)
    show_new_order = fields.Boolean(compute='_compute_show_new_order', store=False)
    @api.depends('valid_garantie')
    def _compute_show_cause(self):
        for record in self:
            record.show_cause = record.valid_garantie and record.valid_garantie == 'oui'
    
    @api.depends('valid_garantie', 'cause')
    def _compute_show_new_order(self):
        for record in self:
            # Check if warranty is expired (valid_garantie date is in the past)
            warranty_expired = record.valid_garantie and record.valid_garantie == 'non'
            # Show new_order_id if no warranty, warranty expired, or if warranty with user default
            record.show_new_order = not record.valid_garantie or warranty_expired or (record.valid_garantie and record.cause == 'defaut_util')
            
    num_serie = fields.Char(string="Numéro de série")
            
    num_serie = fields.Char(string="Numéro de série")
    natur_piece = fields.Char(string="Nature de la pièce")
    spec_tech = fields.Char(string="Spécification technique")
    observation = fields.Text(string="Observations")
    cause = fields.Selection([('defaut_util','Facturé'),('defaut_fab','Non Facturé')],string="Cause")
    new_order_id = fields.Many2one('sale.order', string='Nouveau Bon de Commande', domain="[('partner_id', '=', partner_id)]")

    @api.depends('order_id.post_installation_id.sending_date')
    def _compute_post_installation_sending_date(self):
        for complaint in self:
            if complaint.order_id and complaint.order_id.post_installation_id:
                complaint.post_installation_sending_date = complaint.order_id.post_installation_id.sending_date
            else:
                complaint.post_installation_sending_date = False
    

    def set_all(self):
        for rec in self:
            continue

    def action_preview_attachment(self):
        self.ensure_one()
        self.env.cr.execute("""
            SELECT id 
            FROM ir_attachment 
            WHERE res_model = 'suivi.labo' and res_id = %s
        """, (self.id,))

        attachment_id = self.env.cr.fetchone()  # Fetch first result
        if not attachment_id:
            raise UserError(_('No file to preview.'))
        return {
             'type': 'ir.actions.act_url',
            'url': '/web/content/%s' % attachment_id,
            'target': 'new',
        }

