<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_bank_statement_line_search" model="ir.ui.view">
        <field name="name">account.bank.statement.line.search</field>
        <field name="model">account.bank.statement.line</field>
        <field name="arch" type="xml">
            <search string="Search Lignes de relevé bancaire">
                <field name="payment_ref"/>
                <field name="partner_id"/>
                <field name="journal_id"/>
                <field name="statement_id"/>
                <field name="name"/>
                <field name="date_maturity"/>
                <separator/>
                <filter string="Posted" name="posted" domain="[('state', '=', 'posted')]"/>
                <filter string="Unposted" name="unposted" domain="[('state', '=', 'draft')]"/>
                <separator/>
                <filter string="Date" name="date" date="date"/>
                <group expand="0" string="Group By">
                    <filter string="Partner" name="partner" domain="[]" context="{'group_by': 'partner_id'}"/>
                    <filter string="Journal" name="journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                    <filter string="Statement" name="statement" domain="[]" context="{'group_by': 'statement_id'}"/>
                    <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_bank_statement_line" model="ir.actions.act_window">
        <field name="name">Lignes de relevé bancaire</field>
        <field name="res_model">account.bank.statement.line</field>
        <field name="view_mode">tree</field>
        <field name="search_view_id" ref="view_bank_statement_line_search"/>
        <field name="context">{'search_default_posted': 1}</field>
    </record>

    <menuitem id="menu_bank_statement_line"
              name="Lignes de relevé bancaire"
              action="action_bank_statement_line"
              parent="account.menu_finance_configuration"
              sequence="10"/>
</odoo>
