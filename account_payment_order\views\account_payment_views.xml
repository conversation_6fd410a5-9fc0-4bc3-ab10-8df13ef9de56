<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_account_payment_tree_payment_order" model="ir.ui.view">
        <field name="name">account.payment.tree</field>
        <field name="model">account.payment</field>
        <field name="mode">primary</field>
        <field name="inherit_id" ref="account.view_account_payment_tree" />
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="payment_reference" />
            </field>
        </field>
    </record>
    <record id="view_account_payment_form" model="ir.ui.view">
        <field name="name">account.payment.form</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form" />
        <field name="arch" type="xml">
            <field name="ref" position="after">
                <field
                    name="payment_reference"
                    attrs="{'invisible': [('payment_reference', '=', False)]}"
                />
            </field>
        </field>
    </record>
</odoo>
