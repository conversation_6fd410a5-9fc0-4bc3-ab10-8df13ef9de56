<?xml version="1.0" encoding="utf-8" ?>
<!--
  Copyright 2016 Akretion (http://www.akretion.com/)
  License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl).
-->
<odoo>
    <record id="view_move_line_form" model="ir.ui.view">
        <field name="name">account_payment_partner.move_line_form</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_form" />
        <field name="arch" type="xml">
            <xpath
                expr="//group[@groups='analytic.group_analytic_accounting,analytic.group_analytic_tags']"
                position="after"
            >
                <group name="payments" string="Payments">
                    <field name="account_internal_type" invisible="1" />
                    <field name="reconciled" invisible="1" />
                    <field
                        name="payment_mode_id"
                        widget="selection"
                        force_save="1"
                        attrs="{'invisible': [('account_internal_type', 'not in', ['receivable', 'payable'])], 'readonly': ['|', ('account_internal_type', 'not in', ['receivable', 'payable']), ('reconciled', '=', True)]}"
                    />
                </group>
            </xpath>
        </field>
    </record>
    <record id="view_move_line_tree" model="ir.ui.view">
        <field name="name">account.move.line.tree - Add payment mode</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_tree" />
        <field name="arch" type="xml">
            <field name="date_maturity" position="after">
                <field name="account_internal_type" invisible="1" />
                <field name="reconciled" invisible="1" />
                <field
                    name="payment_mode_id"
                    optional="hide"
                    widget="selection"
                    force_save="1"
                    attrs="{'invisible': [('account_internal_type', 'not in', ['receivable', 'payable'])], 'readonly': ['|', ('account_internal_type', 'not in', ['receivable', 'payable']), ('reconciled', '=', True)]}"
                />
            </field>
        </field>
    </record>
</odoo>
