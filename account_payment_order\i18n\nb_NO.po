# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_payment_order
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-23 03:38+0000\n"
"PO-Revision-Date: 2017-11-23 03:38+0000\n"
"Last-Translator: OCA Transbot <<EMAIL>>, 2017\n"
"Language-Team: <PERSON>ål (Norway) (https://www.transifex.com/oca/"
"teams/23907/nb_NO/)\n"
"Language: nb_NO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"%(count)d payment lines added to the existing draft payment order <a href=# "
"data-oe-model=account.payment.order data-oe-id=%(order_id)d>%(name)s</a>."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"%(count)d payment lines added to the new draft payment order <a href=# data-"
"oe-model=account.payment.order data-oe-id=%(order_id)d>%(name)s</a>, which "
"has been automatically created."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "<b>Account Number</b>: %(number)s - <b>Partner</b>: %(name)s"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Company Currency:</strong>"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Execution:</strong>"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Payment Type:</strong>"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Reference</strong>"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Total</strong>"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Used Account:</strong>"
msgstr ""

#. module: account_payment_order
#: model:ir.model.constraint,message:account_payment_order.constraint_account_payment_line_name_company_unique
msgid "A payment line already exists with this reference in the same company!"
msgstr ""
"En betalingslinje eksisterer allerede med denne referansen i det samme "
"firmaet!"

#. module: account_payment_order
#: code:addons/account_payment_order/models/res_bank.py:0
#, python-format
msgid ""
"A valid BIC contains 8 or 11 characters. The BIC '%(bic)s' contains %(num)d "
"characters, so it is not valid."
msgstr ""

#. module: account_payment_order
#: model:res.groups,name:account_payment_order.group_account_payment
msgid "Accounting / Payments"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_needaction
msgid "Action Needed"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_line__bank_account_required
msgid ""
"Activate this option if this payment method requires you to know the bank "
"account number of your customer or supplier."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_ids
msgid "Activities"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_state
msgid "Activity State"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Add All Move Lines"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_move_form
msgid "Add to Debit Order"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_move_form
msgid "Add to Payment Order"
msgstr "Legg til betalingsordre"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_invoice_create_account_payment_line_action
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_invoice_tree
msgid "Add to Payment/Debit Order"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__target_move__all
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_target_move__all
msgid "All Entries"
msgstr "Alle oppføringer"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__target_move__posted
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_target_move__posted
msgid "All Posted Entries"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__allow_blocked
msgid "Allow Litigation Move Lines"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__allowed_journal_ids
msgid "Allowed journals"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__amount_currency
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_tree
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Amount"
msgstr "Sum"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__amount_company_currency
msgid "Amount in Company Currency"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__payment_mode__any
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_payment_mode__any
msgid "Any"
msgstr "Noen"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_attachment_count
#, fuzzy
msgid "Attachment Count"
msgstr "Vedlegg"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "Attachments"
msgstr "Vedlegg"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Back to Draft"
msgstr "Tilbake til Drøftinger"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_res_bank
msgid "Bank"
msgstr "Bank"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Bank Account"
msgstr "Bankkonto"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__bank_account_required
msgid "Bank Account Required"
msgstr "Bankkonto påkrevd"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__journal_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Bank Journal"
msgstr "Bankjournal"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_move_line__partner_bank_id
msgid "Bank account on which we should pay the supplier"
msgstr "Bankkonto som vi skal betale til leverandøren med"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__cancel
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Cancel"
msgstr "Lukk"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Cancel Payments"
msgstr "Kanseller betalinger"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Choose Move Lines Filter Options"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid ""
"Click on Add All Move Lines to auto-select the move lines matching the above "
"criteria or click on Add an item to manually select the move lines filtered "
"by the above criteria."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__communication
msgid "Communication"
msgstr "Kommunikasjon"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__communication_type
msgid "Communication Type"
msgstr "Kommunikasjonstype"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_line.py:0
#, python-format
msgid "Communication is empty on payment line %s."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__company_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__company_id
msgid "Company"
msgstr "Firma"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__company_partner_bank_id
msgid "Company Bank Account"
msgstr "Firmabankkonto"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Confirm Payments"
msgstr "Bekreft betalinger"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__open
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Confirmed"
msgstr "Bekrefter"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "Create"
msgstr "Lag"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "Create Payment Lines"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Create Payment Lines from Journal Items"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Create Transactions"
msgstr "Lag transaksjoner"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_line_create_action
msgid "Create Transactions from Move Lines"
msgstr ""

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_invoice_payment_line_multi
msgid "Create payment lines from invoice tree view"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__create_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__create_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__create_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__create_uid
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "Created by"
msgstr "Laget av"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__create_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__create_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__create_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__create_date
msgid "Created on"
msgstr "Laget den"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__company_currency_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__company_currency_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Currency"
msgstr "Myntenhet"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__currency_id
msgid "Currency of the Payment Transaction"
msgstr "Betalingstransaksjonens myntenhet"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
#, fuzzy
msgid "Debit Order"
msgstr "Betalingsordre"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_order_inbound_action
#: model:ir.ui.menu,name:account_payment_order.account_payment_order_inbound_menu
msgid "Debit Orders"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_date_prefered
msgid "Default Payment Execution Date"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__description
msgid "Description"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__no_debit_before_maturity
msgid "Disallow Debit Before Maturity Date"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__display_name
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__display_name
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__display_name
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__draft
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Draft"
msgstr "Drøfting"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_type__due
msgid "Due"
msgstr "Utløper"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__ml_maturity_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__due_date
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__date_type__due
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_prefered__due
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__date_prefered__due
msgid "Due Date"
msgstr "Utløpsdato"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__generated
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Generated"
msgstr "Fil generert"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_generated
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Generation Date"
msgstr "Filgenereringsdato"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "File Successfully Uploaded"
msgstr "Filopplastning vellykket"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_uploaded
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Upload Date"
msgstr "Filopplastningsdato"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__uploaded
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Uploaded"
msgstr "Fil lastet opp"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_prefered__fixed
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__date_prefered__fixed
msgid "Fixed Date"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_follower_ids
msgid "Followers"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__bank_account_link
msgid ""
"For payment modes that are always attached to the same bank account of your "
"company (such as wire transfer from customers or SEPA direct debit from "
"suppliers), select 'Fixed'. For payment modes that are not always attached "
"to the same bank account (such as SEPA Direct debit for customers, wire "
"transfer to suppliers), you should select 'Variable', which means that you "
"will select the bank account on the payment order. If your company only has "
"one bank account, you should always select 'Fixed'."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line__communication_type__normal
msgid "Free"
msgstr "Gratis"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_move__reference_type__none
#, fuzzy
msgid "Free Reference"
msgstr "Betalingsreferanse"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Generate Payment File"
msgstr "Generer betalingsfil"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "Generated File"
msgstr "Generert fil"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__generated_user_id
msgid "Generated by"
msgstr "Generert av"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Group By"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__group_lines
msgid "Group Transactions in Payment Orders"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__has_message
msgid "Has Message"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__id
msgid "ID"
msgstr "ID"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_needaction
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_mode__group_lines
msgid ""
"If this mark is checked, the transaction lines of the payment order will be "
"grouped upon confirmation of the payment order.The grouping will be done "
"only if the following fields matches:\n"
"* Partner\n"
"* Currency\n"
"* Destination Bank Account\n"
"* Payment Date\n"
"and if the 'Communication Type' is 'Free'\n"
"(other modules can set additional fields to restrict the grouping.)"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_mode__no_debit_before_maturity
msgid ""
"If you activate this option on an Inbound payment mode, you will have an "
"error message when you confirm a debit order that has a payment line with a "
"payment date before the maturity date."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_prefered__now
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__date_prefered__now
msgid "Immediately"
msgstr "Umiddelbart"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__payment_type__inbound
msgid "Inbound"
msgstr "Innkommende"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_journal__inbound_payment_order_only
#, fuzzy
msgid "Inbound Payment Order Only"
msgstr "Betalingsordre"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Invoice Ref"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_journal
#, fuzzy
msgid "Journal"
msgstr "Bankjournal"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__move_ids
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Journal Entries"
msgstr "Journaloppføringer"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_move_line
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__move_line_id
msgid "Journal Item"
msgstr "Journalgjenstand"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__journal_ids
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_journal_ids
msgid "Journals Filter"
msgstr "Journalfilter"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Keep empty for using all journals"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Keep empty to use all partners"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_line__communication
msgid "Label of the payment that will be seen by the destinee"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi____last_update
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line____last_update
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create____last_update
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order____last_update
msgid "Last Modified on"
msgstr "Sist modifisert den"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__write_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__write_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__write_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__write_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__write_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__write_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert den"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__bank_account_link
msgid "Link to Bank Account"
msgstr "Lenke til bankkonto"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__invoice
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_invoice
msgid "Linked to an Invoice or Refund"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_main_attachment_id
#, fuzzy
msgid "Main Attachment"
msgstr "Vedlegg"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_ids
msgid "Messages"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "Missing Bank Journal on payment order %s."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_line.py:0
#, python-format
msgid "Missing Partner Bank Account on payment line %s"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "Missing bank account on bank journal '%s'."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_type__move
msgid "Move"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__move_date
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__date_type__move
msgid "Move Date"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__move_line_ids
msgid "Move Lines"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
#, fuzzy
msgid "Name or Description"
msgstr "Samme eller tomme"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"No Payment Line created for invoice %s because its payment mode is not "
"intended for payment orders."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid "No Payment Mode on invoice %s"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid "No pending AR/AP lines to add on %s"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__name
msgid "Number"
msgstr "Antall"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_needaction_counter
#, fuzzy
msgid "Number of Actions"
msgstr "Samme eller tomme"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__move_count
msgid "Number of Journal Entries"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_count
msgid "Number of Payment Transactions"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_has_error_counter
#, fuzzy
msgid "Number of errors"
msgstr "Samme eller tomme"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__old_bank_payment_line_name
msgid "Old Bank Payment Line Name"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"On payment order %(porder)s, the Payment Execution Date is in the past "
"(%(exedate)s)."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_method__payment_order_only
#, fuzzy
msgid "Only for payment orders"
msgstr "Legg til betalingsordre"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_mode_form
msgid "Options for Payment Orders"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__payment_type__outbound
msgid "Outbound"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_journal__outbound_payment_order_only
#, fuzzy
msgid "Outbound Payment Order Only"
msgstr "Betalingsordre"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Partner"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_move_line__partner_bank_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__partner_bank_id
msgid "Partner Bank Account"
msgstr "Partnerbankkonto"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__partner_banks_archive_msg
msgid "Partner Banks Archive Msg"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__partner_ids
msgid "Partners"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__date
msgid "Payment Date"
msgstr "Betalingsdato"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_scheduled
msgid "Payment Execution Date"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_prefered
msgid "Payment Execution Date Type"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "Payment File"
msgstr "Betalingsfil"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_line_ids
msgid "Payment Line"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_line_date
msgid "Payment Line Date"
msgstr ""

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_line_action
#: model:ir.model,name:account_payment_order.model_account_payment_line
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_form
msgid "Payment Lines"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_method_id
msgid "Payment Method"
msgstr "Betalingsmetode"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_method
#, fuzzy
msgid "Payment Methods"
msgstr "Betalingsmetode"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__payment_mode
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_mode_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Payment Mode"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_payment_mode
msgid "Payment Mode on Invoice"
msgstr ""

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_mode
msgid "Payment Modes"
msgstr ""

#. module: account_payment_order
#: model:ir.actions.report,name:account_payment_order.action_print_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_bank_statement_line__payment_order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_move__payment_order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__order_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Payment Order"
msgstr "Betalingsordre"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_bank_statement_line__payment_order_ok
#: model:ir.model.fields,field_description:account_payment_order.field_account_move__payment_order_ok
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_order_ok
#, fuzzy
msgid "Payment Order Ok"
msgstr "Betalingsordre"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_order_outbound_action
#: model:ir.ui.menu,name:account_payment_order.account_payment_order_outbound_menu
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_graph
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_pivot
msgid "Payment Orders"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__name
msgid "Payment Reference"
msgstr "Betalingsreferanse"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_ids
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_tree
msgid "Payment Transactions"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__payment_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_type
msgid "Payment Type"
msgstr "Betalingstype"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_move_line__payment_line_ids
#, fuzzy
msgid "Payment lines"
msgstr "Betalingsfil"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__payment_ids
msgid "Payment transaction"
msgstr ""

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_form
msgid "Payments"
msgstr "Betalinger"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_bank_statement_line__reference_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_move__reference_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__reference_type
#, fuzzy
msgid "Reference Type"
msgstr "Betalingsreferanse"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__payment_mode__same
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_payment_mode__same
msgid "Same"
msgstr "Samme"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__payment_mode__same_or_null
msgid "Same or Empty"
msgstr "Samme eller tomme"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_payment_mode__same_or_null
msgid "Same or empty"
msgstr "Samme eller tomme"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Search Payment Orders"
msgstr "Søk for betalingsordre"

#. module: account_payment_order
#: code:addons/account_payment_order/wizard/account_payment_line_create.py:0
#, python-format
msgid "Select Move Lines to Create Transactions"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_mode_form
msgid "Select Move Lines to Pay - Default Values"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__date_scheduled
msgid ""
"Select a requested date of execution if you selected 'Due Date' as the "
"Payment Execution Date Type."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__payment_order_ok
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_mode_search
msgid "Selectable in Payment Orders"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Selected Move Lines to Create Transactions"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__state
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "State"
msgstr "Tilstand"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__state
msgid "Status"
msgstr "Status"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_move__reference_type__structured
msgid "Structured Reference"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__target_move
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_target_move
msgid "Target Moves"
msgstr ""

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_report_account_payment_order_print_account_payment_order_main
msgid "Technical model for printing payment order"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"The amount for Partner '%(partner)s' is negative or null (%(amount).2f) !"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "The following bank accounts are archived:"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"The invoice %(move)s is already added in the payment order(s) %(order)s."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid "The invoice %s is not in Posted state"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"The payment mode '%(pmode)s' has the option 'Disallow Debit Before Maturity "
"Date'. The payment line %(pline)s has a maturity date %(mdate)s which is "
"after the computed payment date %(pdate)s."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"The payment type (%(ptype)s) is not the same as the payment type of the "
"payment mode (%(pmode)s)"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "There are no transactions on payment order %s."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "There's at least one validation error:\n"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_line__ml_maturity_date
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_method__payment_order_only
msgid ""
"This option helps enforcing the use of payment orders for some payment "
"methods."
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "This wizard will create payment lines for the selected invoices:"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Total (Currency)"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__total_company_currency
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_tree
msgid "Total Company Currency"
msgstr "Totalt firmabeholdning"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Total Residual"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_tree
msgid "Total in Company Currency"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_line_ids
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Transactions"
msgstr "Transaksjoner"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__date_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_date_type
msgid "Type of Date Filter"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_unread
msgid "Unread Messages"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Value Date"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_line_create
msgid "Wizard to create payment lines"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"You cannot delete an uploaded payment order. You can cancel it in order to "
"do so."
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid ""
"if there are existing draft payment orders for the payment modes of the "
"invoices, the payment lines will be added to those payment orders"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "on"
msgstr "på"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "otherwise, new payment orders will be created (one per payment mode)."
msgstr ""

#~ msgid "Order"
#~ msgstr "Ordre"

#~ msgid "Total Amount"
#~ msgstr "Totalsum"

#~ msgid ""
#~ "A valid BIC contains 8 or 11 characters. The BIC '%s' contains %d "
#~ "characters, so it is not valid."
#~ msgstr ""
#~ "En gyldig BIC inneholder 8 eller 11 tegn. '%s' inneholder %d tegn, så den "
#~ "er ikke gyldig."

#~ msgid "Transfer Account"
#~ msgstr "Transaksjonskonto"

#~ msgid "Transfer Journal"
#~ msgstr "Transaksjonsjournal"

#~ msgid "Due date"
#~ msgstr "Utløpsdato"
