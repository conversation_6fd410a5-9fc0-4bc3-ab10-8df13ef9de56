# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment_order
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2021-11-08 06:35+0000\n"
"Last-Translator: <PERSON> <ibu<PERSON><EMAIL>>\n"
"Language-Team: none\n"
"Language: es_AR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"%(count)d payment lines added to the existing draft payment order <a href=# "
"data-oe-model=account.payment.order data-oe-id=%(order_id)d>%(name)s</a>."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"%(count)d payment lines added to the new draft payment order <a href=# data-"
"oe-model=account.payment.order data-oe-id=%(order_id)d>%(name)s</a>, which "
"has been automatically created."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "<b>Account Number</b>: %(number)s - <b>Partner</b>: %(name)s"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Company Currency:</strong>"
msgstr "<strong>Moneda de la Compañía:</strong>"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Execution:</strong>"
msgstr "<strong>Ejecución:</strong>"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Payment Type:</strong>"
msgstr "<strong>Tipo de Pago:</strong>"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Reference</strong>"
msgstr "<strong>Referencia</strong>"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "<strong>Used Account:</strong>"
msgstr "<strong>Cuenta Usada:</strong>"

#. module: account_payment_order
#: model:ir.model.constraint,message:account_payment_order.constraint_account_payment_line_name_company_unique
msgid "A payment line already exists with this reference in the same company!"
msgstr "Una línea de pago ya existe con esta referencia en la misma compañía!"

#. module: account_payment_order
#: code:addons/account_payment_order/models/res_bank.py:0
#, python-format
msgid ""
"A valid BIC contains 8 or 11 characters. The BIC '%(bic)s' contains %(num)d "
"characters, so it is not valid."
msgstr ""

#. module: account_payment_order
#: model:res.groups,name:account_payment_order.group_account_payment
msgid "Accounting / Payments"
msgstr "Contabilidad / Pagos"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_needaction
msgid "Action Needed"
msgstr "Acción Requerida"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_line__bank_account_required
msgid ""
"Activate this option if this payment method requires you to know the bank "
"account number of your customer or supplier."
msgstr ""
"Active esta opción si este método de pago requiere que conozca el número de "
"cuenta bancaria de su cliente o proveedor."

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_ids
msgid "Activities"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_state
msgid "Activity State"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Add All Move Lines"
msgstr "Añadir todos los apuntes contables"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_move_form
msgid "Add to Debit Order"
msgstr "Añadir a Orden de Débito"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_move_form
msgid "Add to Payment Order"
msgstr "Añadir a Orden de Pago"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_invoice_create_account_payment_line_action
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_invoice_tree
msgid "Add to Payment/Debit Order"
msgstr "Añadir a la Orden de Pago/Débito"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__target_move__all
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_target_move__all
msgid "All Entries"
msgstr "Todos los Asientos"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__target_move__posted
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_target_move__posted
msgid "All Posted Entries"
msgstr "Todos los Asientos Asentados"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__allow_blocked
msgid "Allow Litigation Move Lines"
msgstr "Permitir Apuntes en Litigio"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__allowed_journal_ids
msgid "Allowed journals"
msgstr "Permitir diarios"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__amount_currency
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_tree
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Amount"
msgstr "Importe"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__amount_company_currency
msgid "Amount in Company Currency"
msgstr "Importes en Moneda de Compañía"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__payment_mode__any
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_payment_mode__any
msgid "Any"
msgstr "Cualquiera"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_attachment_count
msgid "Attachment Count"
msgstr "Cuenta de Adjunto"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "Attachments"
msgstr "Adjuntos"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Back to Draft"
msgstr "Volver a Borrador"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_res_bank
msgid "Bank"
msgstr "Banco"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Bank Account"
msgstr "Cuenta Bancaria"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__bank_account_required
msgid "Bank Account Required"
msgstr "Cuenta Bancaria Requerida"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__journal_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Bank Journal"
msgstr "Diario de banco"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_move_line__partner_bank_id
msgid "Bank account on which we should pay the supplier"
msgstr "Cuenta bancaria en la que debemos pagar al proveedor"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__cancel
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Cancel"
msgstr "Cancelar"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Cancel Payments"
msgstr "Cancelar pagos"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Choose Move Lines Filter Options"
msgstr "Escoja las opciones de filtro de apuntes"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid ""
"Click on Add All Move Lines to auto-select the move lines matching the above "
"criteria or click on Add an item to manually select the move lines filtered "
"by the above criteria."
msgstr ""
"Pulse en \"Añadir todos los apuntes\" para auto-seleccionar los apuntes que "
"casen con los criterios de arriba o pulse en \"Añadir un elemento\" para "
"seleccionar manualmente las líneas filtradas por los criterios de arriba."

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__communication
msgid "Communication"
msgstr "Comunicación"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__communication_type
msgid "Communication Type"
msgstr "Tipo de Comunicación"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_line.py:0
#, python-format
msgid "Communication is empty on payment line %s."
msgstr "La comunicación está vacía en la línea de pago %s."

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__company_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__company_id
msgid "Company"
msgstr "Compañía"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__company_partner_bank_id
msgid "Company Bank Account"
msgstr "Cuenta bancaria de la compañía"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Confirm Payments"
msgstr "Confirmar pagos"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__open
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Confirmed"
msgstr "Confirmado"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "Create"
msgstr "Crear"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "Create Payment Lines"
msgstr "Crear líneas de pago"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Create Payment Lines from Journal Items"
msgstr "Importar apuntes contables"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Create Transactions"
msgstr "Crear transacciones"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_line_create_action
msgid "Create Transactions from Move Lines"
msgstr "Crear transacciones de los apuntes"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_invoice_payment_line_multi
msgid "Create payment lines from invoice tree view"
msgstr "Crear líneas de pago desde la factura"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__create_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__create_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__create_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__create_uid
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "Created by"
msgstr "Creado por"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__create_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__create_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__create_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__create_date
msgid "Created on"
msgstr "Creado en"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__company_currency_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__company_currency_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Currency"
msgstr "Moneda"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__currency_id
msgid "Currency of the Payment Transaction"
msgstr "Moneda de la transacción de pago"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Debit Order"
msgstr "Orden de Débito"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_order_inbound_action
#: model:ir.ui.menu,name:account_payment_order.account_payment_order_inbound_menu
msgid "Debit Orders"
msgstr "Órdenes de cobro"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_date_prefered
msgid "Default Payment Execution Date"
msgstr "Fecha de Ejecución del Pago por defecto"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__description
msgid "Description"
msgstr "Descripción"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__no_debit_before_maturity
msgid "Disallow Debit Before Maturity Date"
msgstr "No permitir el adeudo antes de la fecha de vencimiento"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__display_name
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__display_name
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__display_name
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__display_name
msgid "Display Name"
msgstr "Mostrar Nombre"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__draft
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Draft"
msgstr "Borrador"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_type__due
msgid "Due"
msgstr "Vencimiento"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__ml_maturity_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__due_date
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__date_type__due
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_prefered__due
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__date_prefered__due
msgid "Due Date"
msgstr "Fecha de vencimiento"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__generated
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Generated"
msgstr "Fichero generado"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_generated
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Generation Date"
msgstr "Fecha del fichero generado"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "File Successfully Uploaded"
msgstr "Fichero subido satisfactoriamente"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_uploaded
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Upload Date"
msgstr "Fecha de subida del fichero"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__state__uploaded
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "File Uploaded"
msgstr "Fichero subido"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_prefered__fixed
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__date_prefered__fixed
msgid "Fixed Date"
msgstr "Fecha fija"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Socios)"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__bank_account_link
msgid ""
"For payment modes that are always attached to the same bank account of your "
"company (such as wire transfer from customers or SEPA direct debit from "
"suppliers), select 'Fixed'. For payment modes that are not always attached "
"to the same bank account (such as SEPA Direct debit for customers, wire "
"transfer to suppliers), you should select 'Variable', which means that you "
"will select the bank account on the payment order. If your company only has "
"one bank account, you should always select 'Fixed'."
msgstr ""
"Para modos de pago que siempre estarán asociados a la misma cuenta bancaria "
"de la compañía (como las transferencias bancarias de clientes o los débitos "
"directos SEPA de proveedores), selecciona 'Fijado\". Para modos de pagos en "
"los que puede cambiar la cuenta bancaria (como el débito directo de clientes "
"o las transferencias de proveedores), seleccione 'Variable', lo que "
"significa que podrá seleccionar la cuenta bancaria en la orden de pago. Si "
"su compañía tiene una única cuenta bancaria, debería seleccionar siempre "
"'Fijo'."

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line__communication_type__normal
msgid "Free"
msgstr "Libre"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_move__reference_type__none
msgid "Free Reference"
msgstr "Referencia Libre"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Generate Payment File"
msgstr "Generar archivo de pago"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "Generated File"
msgstr "Archivo generado"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__generated_user_id
msgid "Generated by"
msgstr "Generado por"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Group By"
msgstr "Agrupar por"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__group_lines
msgid "Group Transactions in Payment Orders"
msgstr "Agrupar transacciones en las órdenes de pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__has_message
msgid "Has Message"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__id
msgid "ID"
msgstr "ID"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_needaction
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si está marcado, los mensajes nuevos requieren su atención."

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcado, algunos mensajes tienen un error de entrega."

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_mode__group_lines
msgid ""
"If this mark is checked, the transaction lines of the payment order will be "
"grouped upon confirmation of the payment order.The grouping will be done "
"only if the following fields matches:\n"
"* Partner\n"
"* Currency\n"
"* Destination Bank Account\n"
"* Payment Date\n"
"and if the 'Communication Type' is 'Free'\n"
"(other modules can set additional fields to restrict the grouping.)"
msgstr ""
"Si esta casilla está marcada, las líneas de transacciones de la orden de "
"pago serán agrupadas en la confirmación de la misma. La agrupación se "
"realizará sólo si los siguientes campos coinciden:\n"
"* Empresa\n"
"* Moneda\n"
"* Cuenta bancaria destino\n"
"* Fecha de pago\n"
"y si el 'Tipo de comunicación' es 'Libre'\n"
"(otros módulos pueden establecer campos adicionales para restringir la "
"agrupación)"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_mode__no_debit_before_maturity
msgid ""
"If you activate this option on an Inbound payment mode, you will have an "
"error message when you confirm a debit order that has a payment line with a "
"payment date before the maturity date."
msgstr ""
"Si activa esta opción en un modo de pago entrante, obtendrá un mensaje de "
"error cuando confirme una orden de adeudo que tiene una línea de pago con "
"una fecha de pago anterior a la fecha de vencimiento."

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_prefered__now
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__date_prefered__now
msgid "Immediately"
msgstr "Inmediatamente"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__payment_type__inbound
msgid "Inbound"
msgstr "Entrante"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_journal__inbound_payment_order_only
msgid "Inbound Payment Order Only"
msgstr "Solo Orden de Pago Entrante"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Invoice Ref"
msgstr "Referencia de factura"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_is_follower
msgid "Is Follower"
msgstr "Es Seguidor"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_journal
msgid "Journal"
msgstr "Diario"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__move_ids
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Journal Entries"
msgstr "Asientos Contables"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_move
msgid "Journal Entry"
msgstr "Asiento Contable"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_move_line
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__move_line_id
msgid "Journal Item"
msgstr "Apunte Contable"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__journal_ids
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_journal_ids
msgid "Journals Filter"
msgstr "Filtro de Diarios"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Keep empty for using all journals"
msgstr "Dejar vacío para usar todos los diarios"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Keep empty to use all partners"
msgstr "Mantener vacío para usar todos los socios"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_line__communication
msgid "Label of the payment that will be seen by the destinee"
msgstr "Etiqueta del pago que verá el destinatario"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi____last_update
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line____last_update
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create____last_update
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__write_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__write_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__write_uid
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_invoice_payment_line_multi__write_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__write_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__write_date
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__bank_account_link
msgid "Link to Bank Account"
msgstr "Vinculado a la Cuenta Bancaria"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__invoice
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_invoice
msgid "Linked to an Invoice or Refund"
msgstr "Vinculado a una factura o factura rectificativa"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjunto Principal"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_has_error
msgid "Message Delivery error"
msgstr "Error de Entrega del Mensaje"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "Missing Bank Journal on payment order %s."
msgstr "No se ha establecido el diario de banco en la orden %s."

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_line.py:0
#, python-format
msgid "Missing Partner Bank Account on payment line %s"
msgstr "No se ha establecido la cuenta bancaria en la línea de pago %s"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "Missing bank account on bank journal '%s'."
msgstr "Falta la cuenta bancaria para el diario de banco '%s'."

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_date_type__move
msgid "Move"
msgstr "Asiento"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__move_date
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__date_type__move
msgid "Move Date"
msgstr "Fecha del asiento"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__move_line_ids
msgid "Move Lines"
msgstr "Apuntes"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Name or Description"
msgstr "Nombre o descripción"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"No Payment Line created for invoice %s because its payment mode is not "
"intended for payment orders."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid "No Payment Mode on invoice %s"
msgstr "No hay modo de pago en la factura %s"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid "No pending AR/AP lines to add on %s"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__name
msgid "Number"
msgstr "Número"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de Acciones"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__move_count
msgid "Number of Journal Entries"
msgstr "Número de Apuntes Contables"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_count
msgid "Number of Payment Transactions"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de entrega"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes sin leer"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__old_bank_payment_line_name
msgid "Old Bank Payment Line Name"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"On payment order %(porder)s, the Payment Execution Date is in the past "
"(%(exedate)s)."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_method__payment_order_only
msgid "Only for payment orders"
msgstr "Solo para órdenes de pago"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_mode_form
msgid "Options for Payment Orders"
msgstr "Opciones para las órdenes"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_order__payment_type__outbound
msgid "Outbound"
msgstr "Saliente"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_journal__outbound_payment_order_only
msgid "Outbound Payment Order Only"
msgstr "Solo Orden de Pago Saliente"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Partner"
msgstr "Socio"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_move_line__partner_bank_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__partner_bank_id
msgid "Partner Bank Account"
msgstr "Cuenta bancaria"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__partner_banks_archive_msg
msgid "Partner Banks Archive Msg"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__partner_ids
msgid "Partners"
msgstr "Socios"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__date
msgid "Payment Date"
msgstr "Fecha de pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_scheduled
msgid "Payment Execution Date"
msgstr "Fecha de ejecución del pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__date_prefered
msgid "Payment Execution Date Type"
msgstr "Tipo de fecha de ejecución del pago"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "Payment File"
msgstr "Archivo de remesa"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_line_ids
msgid "Payment Line"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_line_date
msgid "Payment Line Date"
msgstr ""

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_line_action
#: model:ir.model,name:account_payment_order.model_account_payment_line
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_form
msgid "Payment Lines"
msgstr "Líneas de Pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_method_id
msgid "Payment Method"
msgstr "Método de pago"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_method
msgid "Payment Methods"
msgstr "Métodos de pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__payment_mode
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_mode_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Payment Mode"
msgstr "Modo de pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_payment_mode
msgid "Payment Mode on Invoice"
msgstr "Modo de pago en la factura"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_mode
msgid "Payment Modes"
msgstr "Modos de pago"

#. module: account_payment_order
#: model:ir.actions.report,name:account_payment_order.action_print_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_bank_statement_line__payment_order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_move__payment_order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__order_id
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__order_id
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Payment Order"
msgstr "Orden de Pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_bank_statement_line__payment_order_ok
#: model:ir.model.fields,field_description:account_payment_order.field_account_move__payment_order_ok
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__payment_order_ok
msgid "Payment Order Ok"
msgstr "Orden de Pago correcta"

#. module: account_payment_order
#: model:ir.actions.act_window,name:account_payment_order.account_payment_order_outbound_action
#: model:ir.ui.menu,name:account_payment_order.account_payment_order_outbound_menu
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_graph
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_pivot
msgid "Payment Orders"
msgstr "Órdenes de pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__name
msgid "Payment Reference"
msgstr "Referencia de pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_ids
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_tree
msgid "Payment Transactions"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__payment_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_type
msgid "Payment Type"
msgstr "Tipo de pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_move_line__payment_line_ids
msgid "Payment lines"
msgstr "Líneas de pago"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__payment_ids
msgid "Payment transaction"
msgstr ""

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_form
msgid "Payments"
msgstr "Pagos"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_bank_statement_line__reference_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_move__reference_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment__reference_type
msgid "Reference Type"
msgstr "Tipo de Referencia"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__payment_mode__same
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_payment_mode__same
msgid "Same"
msgstr "Igual"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_line_create__payment_mode__same_or_null
msgid "Same or Empty"
msgstr "Igual o vacío"

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_payment_mode__default_payment_mode__same_or_null
msgid "Same or empty"
msgstr "Igual o vacío"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "Search Payment Orders"
msgstr "Buscar órdees"

#. module: account_payment_order
#: code:addons/account_payment_order/wizard/account_payment_line_create.py:0
#, python-format
msgid "Select Move Lines to Create Transactions"
msgstr "Seleccione apuntes para crear transacciones"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_mode_form
msgid "Select Move Lines to Pay - Default Values"
msgstr "Selección de los apuntes - Valores por defecto"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__date_scheduled
msgid ""
"Select a requested date of execution if you selected 'Due Date' as the "
"Payment Execution Date Type."
msgstr ""
"Seleccione una fecha solicitada de ejecución si ha seleccionado 'Fecha de "
"vencimiento' como el tipo de fecha de ejecución del pago."

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__payment_order_ok
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_mode_search
msgid "Selectable in Payment Orders"
msgstr "Seleccionable en las órdenes"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Selected Move Lines to Create Transactions"
msgstr "Apuntes seleccionados para crear transacciones"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line__state
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_search
msgid "State"
msgstr "Estado"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__state
msgid "Status"
msgstr "Estatus"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields.selection,name:account_payment_order.selection__account_move__reference_type__structured
msgid "Structured Reference"
msgstr "Referencia esctructurada"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__target_move
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_target_move
msgid "Target Moves"
msgstr "Asientos destino"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_report_account_payment_order_print_account_payment_order_main
msgid "Technical model for printing payment order"
msgstr "Modelo técnico para imprimir una orden de pago"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"The amount for Partner '%(partner)s' is negative or null (%(amount).2f) !"
msgstr ""

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "The following bank accounts are archived:"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid ""
"The invoice %(move)s is already added in the payment order(s) %(order)s."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_move.py:0
#, python-format
msgid "The invoice %s is not in Posted state"
msgstr "La factura %s no está en estado Publicado"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"The payment mode '%(pmode)s' has the option 'Disallow Debit Before Maturity "
"Date'. The payment line %(pline)s has a maturity date %(mdate)s which is "
"after the computed payment date %(pdate)s."
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"The payment type (%(ptype)s) is not the same as the payment type of the "
"payment mode (%(pmode)s)"
msgstr ""

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "There are no transactions on payment order %s."
msgstr "No hay transacciones en la orden %s."

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid "There's at least one validation error:\n"
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_line__ml_maturity_date
msgid ""
"This field is used for payable and receivable journal entries. You can put "
"the limit date for the payment of this line."
msgstr ""
"Este campo se usa para asientos a pagar y a cobrar. Puede poner la fecha "
"límite para el pago de esta línea."

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_method__payment_order_only
msgid ""
"This option helps enforcing the use of payment orders for some payment "
"methods."
msgstr ""
"Esta opción ayuda a forzar el uso de órdenes de pago para algunos métodos de "
"pago."

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "This wizard will create payment lines for the selected invoices:"
msgstr ""
"Este asistente creará las líneas de pago para las facturas seleccionadas:"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Total (Currency)"
msgstr "Total (Moneda)"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__total_company_currency
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_tree
msgid "Total Company Currency"
msgstr "Total en la moneda de la compañía"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_create_form
msgid "Total Residual"
msgstr "Total pendiente"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_line_tree
msgid "Total in Company Currency"
msgstr "Total en moneda de la compañía"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__payment_line_ids
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_payment_order_form
msgid "Transactions"
msgstr "Transacciones"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_line_create__date_type
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_mode__default_date_type
msgid "Type of Date Filter"
msgstr "Tipo de Filtro de Fecha"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_unread
msgid "Unread Messages"
msgstr "Mensajes sin Leer"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contador de Mensajes sin Leer"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.print_account_payment_order_document
msgid "Value Date"
msgstr "Fecha de Valor"

#. module: account_payment_order
#: model:ir.model.fields,field_description:account_payment_order.field_account_payment_order__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del Sitio Web"

#. module: account_payment_order
#: model:ir.model.fields,help:account_payment_order.field_account_payment_order__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: account_payment_order
#: model:ir.model,name:account_payment_order.model_account_payment_line_create
msgid "Wizard to create payment lines"
msgstr "Asistente para crear líneas de pago"

#. module: account_payment_order
#: code:addons/account_payment_order/models/account_payment_order.py:0
#, python-format
msgid ""
"You cannot delete an uploaded payment order. You can cancel it in order to "
"do so."
msgstr ""
"No puede eliminar una orden de pago subida. Puede cancelar la orden para "
"hacerlo."

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid ""
"if there are existing draft payment orders for the payment modes of the "
"invoices, the payment lines will be added to those payment orders"
msgstr ""
"Si hay órdenes borrador existentes para los modos de pago de las facturas, "
"las líneas de pago se añadirán a ellas"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.view_attachment_simplified_form
msgid "on"
msgstr "el"

#. module: account_payment_order
#: model_terms:ir.ui.view,arch_db:account_payment_order.account_invoice_payment_line_multi_form
msgid "otherwise, new payment orders will be created (one per payment mode)."
msgstr ""
"En caso contrario, se crearán nuevas órdenes (una por cada modo de pago)."

#, python-format
#~ msgid ""
#~ "No handler for this payment method. Maybe you haven't installed the "
#~ "related Odoo module."
#~ msgstr ""
#~ "Sin manejador para este método de pago. Tal vez no ha instalado el módulo "
#~ "de Odoo relacionado."

#~ msgid "SMS Delivery error"
#~ msgstr "Error de entrega de SMS"

#, python-format
#~ msgid ""
#~ "No Payment Line created for invoice %s because it already exists or "
#~ "because this invoice is already paid."
#~ msgstr ""
#~ "No se ha creado línea de pago para la factura %s porque ya existe o "
#~ "porque la factura ya está pagada."

#~ msgid "Accounting Entries Options"
#~ msgstr "Opciones de asientos contables"

#~ msgid "Bank Payment Line"
#~ msgstr "Línea de pago bancario"

#~ msgid "Bank Payment Line Ref"
#~ msgstr "Ref. de la línea de pago bancario"

#~ msgid "Bank Payment Lines"
#~ msgstr "Líneas de pago bancario"

#~ msgid "Bank Transactions"
#~ msgstr "Transacciones Bancarias"

#, python-format
#~ msgid "Debit bank line %s"
#~ msgstr "Línea de adeudo de banco %s"

#, python-format
#~ msgid "Debit order %s"
#~ msgstr "Orden de cobro %s"

#~ msgid "Generate Accounting Entries On File Upload"
#~ msgstr "Generar asientos contables al subir el archivo"

#~ msgid "Move Option"
#~ msgstr "Opciones de asiento"

#~ msgid "Number of Bank Transactions"
#~ msgstr "Número de Transacciones Bancarias"

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', you must choose an option for the 'Move Option' "
#~ "parameter."
#~ msgstr ""
#~ "En el modo de pago '%s', debe escoger una opción para las 'Opciones de "
#~ "asiento'."

#~ msgid "One move per payment date"
#~ msgstr "Un asiento por fecha de pago"

#~ msgid "One move per payment line"
#~ msgstr "Un asiento por línea de pago"

#~ msgid "Order"
#~ msgstr "Orden"

#, python-format
#~ msgid "Payment bank line %s"
#~ msgstr "Línea de pago bancario %s"

#, python-format
#~ msgid "Payment order %s"
#~ msgstr "Orden %s"

#~ msgid "Post Move"
#~ msgstr "Publicar movimiento"

#~ msgid "Related Payment Lines"
#~ msgstr "Líneas de Pago Relacionadas"

#~ msgid "Search Bank Payment Lines"
#~ msgstr "Buscar líneas de pago bancario"

#~ msgid ""
#~ "The bank payment lines are used to generate the payment file. They are "
#~ "automatically created from transaction lines upon confirmation of the "
#~ "payment order: one bank payment line can group several transaction lines "
#~ "if the option 'Group Transactions in Payment Orders' is active on the "
#~ "payment mode."
#~ msgstr ""
#~ "Las líneas de pago bancarias se usan para generar el archivo de pago. Se "
#~ "crean automáticamente de las líneas de transacciones en la confirmación "
#~ "de la orden: una línea de pago bancario puede agrupar varias líneas de "
#~ "transacción si la opción 'Agrupar transacciones en las órdenes' está "
#~ "activada en el modo de pago."

#~ msgid "Total Amount"
#~ msgstr "Importe total"

#~ msgid "%d payment lines added to the existing draft payment order %s."
#~ msgstr ""
#~ "%d líneas de pago añadidas a la existente orden de pago borrador %s."

#~ msgid ""
#~ "%d payment lines added to the new draft payment order %s which has been "
#~ "automatically created."
#~ msgstr ""
#~ "%d líneas de pago añadidas a la nueva orden de pago borrador %s la cual "
#~ "fue creada automáticamente."

#~ msgid ""
#~ "A valid BIC contains 8 or 11 characters. The BIC '%s' contains %d "
#~ "characters, so it is not valid."
#~ msgstr ""
#~ "Un BIC válido contiene 8 u 11 caracteres. El BIC '%s' contiene %d "
#~ "caracteres, por lo que no es válido."

#~ msgid "Can not reconcile: no move line for payment line %s of partner '%s'."
#~ msgstr ""
#~ "No se puede conciliar: no hay apunte para la línea de pago %s de la "
#~ "empresa '%s'."

#~ msgid ""
#~ "Cannot delete a payment order line whose payment order is in state '%s'. "
#~ "You need to cancel it first."
#~ msgstr ""
#~ "No se puede eliminar una línea de una orden de pago cuyo estado es '%s'. "
#~ "Primero debes cancelarla."

#~ msgid "Followers (Channels)"
#~ msgstr "Seguidores (Canales)"

#~ msgid ""
#~ "For partner '%s', the account of the account move line to pay (%s) is "
#~ "different from the account of of the transit move line (%s)."
#~ msgstr ""
#~ "Para la empresa '%s', la cuenta del apunte a pagar (%s) es diferente de "
#~ "la cuenta del apunte de tránsito (%s)."

#~ msgid "Move line '%s' of partner '%s' has already been reconciled"
#~ msgstr "El apunte '%s' de la empresa '%s' ya ha sido conciliado"

#~ msgid "On payment order %s, the Payment Execution Date is in the past (%s)."
#~ msgstr "En la orden %s, la fecha de ejecución es anterior a la actual (%s)."

#~ msgid "The amount for Partner '%s' is negative or null (%.2f) !"
#~ msgstr "El importe para el empresa '%s' es negativo o nulo (%.2f) !"

#~ msgid ""
#~ "The payment mode '%s' has the option 'Disallow Debit Before Maturity "
#~ "Date'. The payment line %s has a maturity date %s which is after the "
#~ "computed payment date %s."
#~ msgstr ""
#~ "El modo de pago '%s' tiene la opción 'No permitir adeudo antes de la "
#~ "fecha de vencimiento'. La línea de pago %s tiene una fecha de vencimiento "
#~ "%s que es después de la fecha de pago calculada %s."

#~ msgid ""
#~ "The payment type (%s) is not the same as the payment type of the payment "
#~ "mode (%s)"
#~ msgstr ""
#~ "El tipo de pago (%s) no es el mismo que el tipo de pago del modo de pago "
#~ "(%s)"

#~ msgid "Done"
#~ msgstr "Realizado"

#~ msgid "Done Date"
#~ msgstr "Fecha de realización"

#~ msgid ""
#~ "Journal to write payment entries when confirming payment/debit orders of "
#~ "this mode"
#~ msgstr ""
#~ "Diario al que escribir los asientos contables cuando se confirme la orden "
#~ "de cobro/pago de este modo"

#~ msgid "Number of Bank Lines"
#~ msgstr "Número de líneas bancarias"

#~ msgid "Offsetting Account"
#~ msgstr "Cuenta de compensación"

#~ msgid ""
#~ "On the payment mode '%s', you must select a value for the 'Transfer "
#~ "Account'."
#~ msgstr ""
#~ "En el modo de pago '%s', debe seleccionar un valor para la 'Cuenta de "
#~ "transferencia'."

#~ msgid ""
#~ "On the payment mode '%s', you must select a value for the 'Transfer "
#~ "Journal'."
#~ msgstr ""
#~ "En el modo de pago '%s', debe seleccionar un valor para el 'Diario de "
#~ "transferencia'."

#~ msgid ""
#~ "On the payment mode '%s', you must select an option for the 'Offsetting "
#~ "Account' parameter"
#~ msgstr ""
#~ "En el modo de pago '%s', debe seleccionar una opción para el parámetro "
#~ "'Cuenta de compensación'"

#~ msgid ""
#~ "Pay off lines in 'file uploaded' payment orders with a move on this "
#~ "account. You can only select accounts that are marked for reconciliation"
#~ msgstr ""
#~ "Las líneas de pago de la orden se conciliarán en la 'subida de archivo' "
#~ "con un apunte a esta cuenta. Sólo puede seleccionar las cuentas que están "
#~ "marcadas para conciliación"

#~ msgid "Transaction Lines"
#~ msgstr "Líneas de transacción"

#~ msgid "Transfer Account"
#~ msgstr "Cuenta de Transferencia"

#~ msgid "Transfer Journal"
#~ msgstr "Diario de Transferencia"

#~ msgid "Transfer Journal Entries"
#~ msgstr "Asientos de Transferencia"
