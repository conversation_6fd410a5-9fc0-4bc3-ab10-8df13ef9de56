<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- VIEWS -->

        <record id="view_account_payment_search_inherit_account_batch_payment" model="ir.ui.view">
            <field name="name">account.payment.search.inherit.account_batch_payment</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_search" />
            <field name="arch" type="xml">

                <!-- Filter on batch payments -->
                <filter name="transfers_filter" position="after">
                    <filter string="Batch Payments"
                            name="batch_payments_filter"
                            domain="[('batch_payment_id', '!=', False)]"/>
                </filter>

                <!-- Group by batch payments -->
                <filter name="paymentmethodline" position="after">
                    <filter string="Batch Payments"
                            name="batch_payments"
                            context="{'group_by': 'batch_payment_id'}"/>
                </filter>

                <filter name="reconciled" position="after">
                    <filter string="Not reconciled" name="not_reconciled" domain="[('is_reconciled', '=', False)]"/>
                </filter>

            </field>
        </record>

        <record id="view_account_payment_tree_inherit_account_batch_payment" model="ir.ui.view">
            <field name="name">account.payment.tree.inherit.account_batch_payment</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_tree" />
            <field name="arch" type="xml">
                <field name="partner_id" position="after">
                    <field name="batch_payment_id"/>
                </field>
            </field>
        </record>

        <record id="view_account_payment_form_inherit_account_batch_payment" model="ir.ui.view">
            <field name="name">account.payment.form.inherit.account_batch_payment</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_form" />
            <field name="arch" type="xml">

                <div name="button_box" position="inside">
                    <!-- Batch payment stat button -->
                    <field name="batch_payment_id" invisible="1"/>
                    <button name="button_open_batch_payment" type="object"
                            class="oe_stat_button" icon="fa-bars"
                            attrs="{'invisible': [('batch_payment_id','=', False)]}">
                        <span>Batch Payment</span>
                    </button>
                </div>

            </field>
        </record>

    </data>
</odoo>
