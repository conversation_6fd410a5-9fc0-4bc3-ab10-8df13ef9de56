
from odoo import api, fields, models, Command, tools, _
from odoo.tools import float_compare, float_is_zero
from odoo.osv.expression import get_unaccent_wrapper
from odoo.exceptions import UserError, ValidationError
import re
from math import copysign
from collections import defaultdict
from dateutil.relativedelta import relativedelta

import logging
_logger = logging.getLogger(__name__)

class AccountReconcileModel(models.Model):
    _inherit = 'account.reconcile.model'

    def _get_invoice_matching_rule_result(self, st_line, candidates, aml_ids_to_exclude, reconciled_amls_ids, partner):
        new_reconciled_aml_ids = set()
        new_treated_aml_ids = set()
        candidates, priorities = self._filter_candidates(candidates, aml_ids_to_exclude, reconciled_amls_ids)
        _logger.info("aml_ids_to_exclude %s",aml_ids_to_exclude)
        _logger.info("reconciled_amls_ids %s",reconciled_amls_ids)
        _logger.info("candidates %s",candidates)
        _logger.info("priorities %s",priorities)
        st_line_currency = st_line.foreign_currency_id or st_line.currency_id
        candidate_currencies = set(candidate['aml_currency_id'] for candidate in candidates)
        kept_candidates = candidates
        if candidate_currencies == {st_line_currency.id}:
            kept_candidates = []
            sum_kept_candidates = 0
            for candidate in candidates:
                candidate_residual = candidate['aml_amount_residual_currency']
                _logger.info("candidate id %s",candidate["aml_id"])
                _logger.info("candidate_residual %s",candidate_residual)
                _logger.info("aml_date_maturity %s",candidate["aml_date_maturity"])
                _logger.info("aml_date_maturity %s",st_line.date_maturity)
                if st_line_currency.compare_amounts(candidate_residual, -st_line.amount_residual) == 0 and candidate["aml_date_maturity"] == st_line.date_maturity and st_line.date_maturity:
                    kept_candidates = [candidate]
                    break
                elif st_line_currency.compare_amounts(candidate_residual, -st_line.amount_residual) == 0 and candidate["aml_date_maturity"] == st_line.date:
                    kept_candidates = [candidate]
                    break
                elif st_line_currency.compare_amounts(abs(sum_kept_candidates), abs(st_line.amount_residual)) < 0:
                    # Candidates' and statement line's balances have the same sign, thanks to _get_invoice_matching_query.
                    # We hence can compare their absolute value without any issue.
                    # Here, we still have room for other candidates ; so we add the current one to the list we keep.
                    # Then, we continue iterating, even if there is no room anymore, just in case one of the following candidates
                    # is an exact match, which would then be preferred on the current candidates.
                    kept_candidates.append(candidate)
                    sum_kept_candidates += candidate_residual

        # It is possible kept_candidates now contain less different priorities; update them
        _logger.info("kept_candidates %s",kept_candidates)
        kept_candidates_by_priority = self._sort_reconciliation_candidates_by_priority(kept_candidates, aml_ids_to_exclude, reconciled_amls_ids)
        _logger.info("kept_candidates_by_priority %s",kept_candidates_by_priority)
        priorities = set(kept_candidates_by_priority.keys())
        _logger.info("priorities %s",priorities)
        # We check the amount criteria of the reconciliation model, and select the
        # kept_candidates if they pass the verification.
        matched_candidates_values = self._process_matched_candidates_data(st_line, kept_candidates)
        _logger.info("matched_candidates_values %s",matched_candidates_values)
        status = self._check_rule_propositions(matched_candidates_values)
        if 'rejected' in status:
            rslt = None
        else:
            rslt = {
                'model': self,
                'aml_ids': [candidate['aml_id'] for candidate in kept_candidates],
            }
            new_treated_aml_ids = set(rslt['aml_ids'])

            # Create write-off lines (in company's currency).
            if 'allow_write_off' in status:
                residual_balance_after_rec = matched_candidates_values['residual_balance_curr'] + matched_candidates_values['candidates_balance_curr']
                writeoff_vals_list = self._get_write_off_move_lines_dict(
                    st_line,
                    matched_candidates_values['balance_sign'] * residual_balance_after_rec,
                    partner.id,
                )
                if writeoff_vals_list:
                    rslt['status'] = 'write_off'
                    rslt['write_off_vals'] = writeoff_vals_list
            else:
                writeoff_vals_list = []
            _logger.info("writeoff_vals_list %s",writeoff_vals_list)
            # Reconcile.
            if 'allow_auto_reconcile' in status:

                # Process auto-reconciliation. We only do that for the first two priorities, if they are not matched elsewhere.
                aml_ids = [candidate['aml_id'] for candidate in kept_candidates]
                _logger.info("aml_ids %s",aml_ids)
                lines_vals_list = [{'id': aml_id} for aml_id in aml_ids]
                if lines_vals_list and self.auto_reconcile:

                    # Ensure this will not raise an error if case of missing account to create an open balance.
                    dummy, open_balance_vals = st_line._prepare_reconciliation(lines_vals_list + writeoff_vals_list)

                    if not open_balance_vals or open_balance_vals.get('account_id'):

                        if not st_line.partner_id and partner:
                            st_line.partner_id = partner

                        st_line.reconcile(lines_vals_list + writeoff_vals_list, allow_partial=True)

                        rslt['status'] = 'reconciled'
                        rslt['reconciled_lines'] = st_line.line_ids
                        new_reconciled_aml_ids = new_treated_aml_ids

        return rslt, new_reconciled_aml_ids, new_treated_aml_ids

    


    def _get_rule_result(self, st_line, candidates, aml_ids_to_exclude, reconciled_amls_ids, partner_map):
        """ Get the result of a rule from the list of available candidates, depending on the
        other reconciliations performed by previous rules.
        """
        self.ensure_one()
        _logger.info("rule type %s",self.rule_type)
        if self.rule_type == 'invoice_matching':
            return self._get_invoice_matching_rule_result(st_line, candidates, aml_ids_to_exclude, reconciled_amls_ids, partner_map)
        elif self.rule_type == 'writeoff_suggestion':
            return self._get_writeoff_suggestion_rule_result(st_line, partner_map), set(), set()
        else:
            return None, set(), set()

    def _apply_rules(self, st_lines, excluded_ids=None, partner_map=None):
        ''' Apply criteria to get candidates for all reconciliation models.

        This function is called in enterprise by the reconciliation widget to match
        the statement lines with the available candidates (using the reconciliation models).

        :param st_lines:        Account.bank.statement.lines recordset.
        :param excluded_ids:    Account.move.lines to exclude.
        :param partner_map:     Dict mapping each line with new partner eventually.
        :return:                A dict mapping each statement line id with:
            * aml_ids:      A list of account.move.line ids.
            * model:        An account.reconcile.model record (optional).
            * status:       'reconciled' if the lines has been already reconciled, 'write_off' if the write-off must be
                            applied on the statement line.
        '''
        # This functions uses SQL to compute its results. We need to flush before doing anything more.
        for model_name in ('account.bank.statement', 'account.bank.statement.line', 'account.move', 'account.move.line', 'res.company', 'account.journal', 'account.account'):
            self.env[model_name].flush(self.env[model_name]._fields)

        results = {line.id: {'aml_ids': []} for line in st_lines}
        _logger.info("rule results %s",results)
        available_models = self.filtered(lambda m: m.rule_type != 'writeoff_button').sorted()
        aml_ids_to_exclude = set() # Keep track of already processed amls.
        reconciled_amls_ids = set() # Keep track of already reconciled amls.

        # First associate with each rec models all the statement lines for which it is applicable
        lines_with_partner_per_model = defaultdict(lambda: [])
        # Exclude already in the statement line associated account move lines
        excluded_ids = excluded_ids or [] + st_lines.move_id.line_ids.ids
        for st_line in st_lines:

            # Statement lines created in old versions could have a residual amount of zero. In that case, don't try to
            # match anything.
            if not st_line.amount_residual:
                continue

            mapped_partner = (partner_map and partner_map.get(st_line.id) and self.env['res.partner'].browse(partner_map[st_line.id])) or st_line.partner_id

            for rec_model in available_models:
                partner = mapped_partner or rec_model._get_partner_from_mapping(st_line)

                if rec_model._is_applicable_for(st_line, partner):
                    lines_with_partner_per_model[rec_model].append((st_line, partner))

        # Execute only one SQL query for each model (for performance)
        matched_lines = self.env['account.bank.statement.line']
        for rec_model in available_models:

            # We filter the lines for this model, in case a previous one has already found something for them
            filtered_st_lines_with_partner = [x for x in lines_with_partner_per_model[rec_model] if x[0] not in matched_lines]
            _logger.info("rule filtered_st_lines_with_partner %s",filtered_st_lines_with_partner)
            if not filtered_st_lines_with_partner:
                # No unreconciled statement line for this model
                continue

            all_model_candidates = rec_model._get_candidates(filtered_st_lines_with_partner, excluded_ids)
            _logger.info("rule all_model_candidates %s",all_model_candidates)
            for st_line, partner in filtered_st_lines_with_partner:
                candidates = all_model_candidates[st_line.id]
                if candidates:
                    model_rslt, new_reconciled_aml_ids, new_treated_aml_ids = rec_model._get_rule_result(st_line, candidates, aml_ids_to_exclude, reconciled_amls_ids, partner)
                    _logger.info("rule model_rslt %s",model_rslt)
                    _logger.info("rule new_reconciled_aml_ids %s",new_reconciled_aml_ids)
                    _logger.info("rule new_treated_aml_ids %s",new_treated_aml_ids)
                    if model_rslt:
                        # We inject the selected partner (possibly coming from the rec model)
                        model_rslt['partner']= partner

                        results[st_line.id] = model_rslt
                        reconciled_amls_ids |= new_reconciled_aml_ids
                        aml_ids_to_exclude |= new_treated_aml_ids
                        matched_lines += st_line
                _logger.info("rule resultss %s",results)
        return results

    
    def _get_invoice_matching_query(self, st_lines_with_partner, excluded_ids):
        ''' Returns the query applying the current invoice_matching reconciliation
        model to the provided statement lines.

        :param st_lines_with_partner: A list of tuples (statement_line, partner),
                                      associating each statement line to treate with
                                      the corresponding partner, given by the partner map
        :param excluded_ids:    Account.move.lines to exclude.
        :return:                (query, params)
        '''
        self.ensure_one()
        if self.rule_type != 'invoice_matching':
            raise UserError(_('Programmation Error: Can\'t call _get_invoice_matching_query() for different rules than \'invoice_matching\''))

        unaccent = get_unaccent_wrapper(self._cr)

        # N.B: 'communication_flag' is there to distinguish invoice matching through the number/reference
        # (higher priority) from invoice matching using the partner (lower priority).
        query = r'''
        SELECT
            st_line.id                          AS id,
            aml.id                              AS aml_id,
            aml.currency_id                     AS aml_currency_id,
            aml.date_maturity                   AS aml_date_maturity,
            aml.amount_residual                 AS aml_amount_residual,
            aml.amount_residual_currency        AS aml_amount_residual_currency,
            ''' + self._get_select_communication_flag() + r''' AS communication_flag,
            ''' + self._get_select_payment_reference_flag() + r''' AS payment_reference_flag
        FROM account_bank_statement_line st_line
        JOIN account_move st_line_move          ON st_line_move.id = st_line.move_id
        JOIN res_company company                ON company.id = st_line_move.company_id
        , account_move_line aml
        LEFT JOIN account_move move             ON move.id = aml.move_id AND move.state = 'posted'
        LEFT JOIN account_account account       ON account.id = aml.account_id
        LEFT JOIN res_partner aml_partner       ON aml.partner_id = aml_partner.id
        LEFT JOIN account_payment payment       ON payment.move_id = move.id
        WHERE
            aml.company_id = st_line_move.company_id
            AND move.state = 'posted'
            AND account.reconcile IS TRUE
            AND aml.reconciled IS FALSE
            AND (account.internal_type NOT IN ('receivable', 'payable') OR aml.payment_id IS NULL)
        '''

        # Add conditions to handle each of the statement lines we want to match
        st_lines_queries = []
        for st_line, partner in st_lines_with_partner:
            # In case we don't have any partner for this line, we try assigning one with the rule mapping
            if st_line.amount > 0:
                st_line_subquery = r"aml.balance > 0"
            else:
                st_line_subquery = r"aml.balance < 0"
            st_line_subquery += r" AND aml.journal_id = %s" % st_line.journal_id.id
            if self.match_same_currency:
                st_line_subquery += r" AND COALESCE(aml.currency_id, company.currency_id) = %s" % (st_line.foreign_currency_id.id or st_line.move_id.currency_id.id)
            _logger.info("st_line.x_move_line_associated %s",st_line.x_move_line_associated)
            if st_line.x_move_line_associated:
                st_line_subquery += " AND aml.id = %s" % st_line.x_move_line_associated
            if partner:
                st_line_subquery += r" AND aml.partner_id = %s" % partner.id
            else:
                st_line_fields_consideration = [
                    (self.match_text_location_label, 'st_line.payment_ref'),
                    (self.match_text_location_note, 'st_line_move.narration'),
                    (self.match_text_location_reference, 'st_line_move.ref'),
                ]

                no_partner_query = " OR ".join([
                    r"""
                        (
                            substring(REGEXP_REPLACE(""" + sql_field + """, '[^0-9\s]', '', 'g'), '\S(?:.*\S)*') != ''
                            AND
                            (
                                (""" + self._get_select_communication_flag() + """)
                                OR
                                (""" + self._get_select_payment_reference_flag() + """)
                            )
                        )
                        OR
                        (
                            /* We also match statement lines without partners with amls
                            whose partner's name's parts (splitting on space) are all present
                            within the payment_ref, in any order, with any characters between them. */

                            aml_partner.name IS NOT NULL
                            AND """ + unaccent(sql_field) + r""" ~* ('^' || (
                                SELECT string_agg(concat('(?=.*\m', chunk[1], '\M)'), '')
                                  FROM regexp_matches(""" + unaccent("aml_partner.name") + r""", '\w{3,}', 'g') AS chunk
                            ))
                        )
                    """
                    for consider_field, sql_field in st_line_fields_consideration
                    if consider_field
                ])

                if no_partner_query:
                    st_line_subquery += " AND " + no_partner_query

            st_lines_queries.append(r"st_line.id = %s AND (%s)" % (st_line.id, st_line_subquery))

        query += r" AND (%s) " % " OR ".join(st_lines_queries)

        params = {}

        # If this reconciliation model defines a past_months_limit, we add a condition
        # to the query to only search on move lines that are younger than this limit.
        if self.past_months_limit:
            date_limit = fields.Date.context_today(self) - relativedelta(months=self.past_months_limit)
            query += " AND aml.date >= %(aml_date_limit)s"
            params['aml_date_limit'] = date_limit

        # Filter out excluded account.move.line.
        if excluded_ids:
            query += ' AND aml.id NOT IN %(excluded_aml_ids)s'
            params['excluded_aml_ids'] = tuple(excluded_ids)

        if self.matching_order == 'new_first':
            query += ' ORDER BY aml_date_maturity DESC, aml_id DESC'
        else:
            query += ' ORDER BY aml_date_maturity ASC, aml_id ASC'

        return query, params