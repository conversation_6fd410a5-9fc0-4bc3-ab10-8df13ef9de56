# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment_mode
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2021-04-24 20:47+0000\n"
"Last-Translator: <PERSON> <ibu<PERSON><EMAIL>>\n"
"Language-Team: none\n"
"Language: es_AR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_method__bank_account_required
msgid ""
"Activate this option if this payment method requires you to know the bank "
"account number of your customer or supplier."
msgstr ""
"Active esta opción si este método de pago requiere que conozca el número de "
"cuenta bancaria de su cliente o proveedor."

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__active
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__active
msgid "Active"
msgstr "Activo"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__variable_journal_ids
msgid "Allowed Bank Journals"
msgstr "Diarios de Banco Permitidos"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_form
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Archived"
msgstr "Archivado"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__bank_account_required
msgid "Bank Account Required"
msgstr "Cuenta Bancaria Requerida"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__code
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_method_code
msgid "Code (Do Not Modify)"
msgstr "Código (no modificar)"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__company_id
msgid "Company"
msgstr "Compañía"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__create_date
msgid "Created on"
msgstr "Creado en"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_ct1
msgid "Credit Transfer to Suppliers"
msgstr "Orden de pago a proveedores"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_dd1
msgid "Direct Debit of customers"
msgstr "Orden de débito de clientes"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_dd2
msgid "Direct Debit of suppliers from La Banque Postale"
msgstr "Deuda directa de proveedores de La Banque Postale"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_dd1
msgid "Direct Debit of suppliers from Société Générale"
msgstr "Deuda directa de proveedores de Société Générale"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__display_name
msgid "Display Name"
msgstr "Mostrar Nombre"

#. module: account_payment_mode
#: model:ir.model.fields.selection,name:account_payment_mode.selection__account_payment_mode__bank_account_link__fixed
msgid "Fixed"
msgstr "Fijo"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__fixed_journal_id
msgid "Fixed Bank Journal"
msgstr "Diario de Banco Fijo"

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_mode__bank_account_link
msgid ""
"For payment modes that are always attached to the same bank account of your "
"company (such as wire transfer from customers or SEPA direct debit from "
"suppliers), select 'Fixed'. For payment modes that are not always attached "
"to the same bank account (such as SEPA Direct debit for customers, wire "
"transfer to suppliers), you should select 'Variable', which means that you "
"will select the bank account on the payment order. If your company only has "
"one bank account, you should always select 'Fixed'."
msgstr ""
"Para modos de pago que siempre estarán asociados a la misma cuenta bancaria "
"de la compañía (como las transferencias bancarias de clientes o los débitos "
"directos SEPA de proveedores), selecciona 'Fijado\". Para modos de pagos en "
"los que puede cambiar la cuenta bancaria (como el débito directo de clientes "
"o las transferencias de proveedores), seleccione 'Variable', lo que "
"significa que podrá seleccionar la cuenta bancaria en la orden de pago. Si "
"su compañía tiene una única cuenta bancaria, debería seleccionar siempre "
"'Fijo'."

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Group By"
msgstr "Agrupar por"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__id
msgid "ID"
msgstr "ID"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Inbound"
msgstr "Entrante"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_ct2
msgid "Inbound Credit Trf La Banque Postale"
msgstr "Transf. entrante La Banque Postale"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_ct1
msgid "Inbound Credit Trf Société Générale"
msgstr "Transf. entrante Société Générale"

#. module: account_payment_mode
#: model:ir.model,name:account_payment_mode.model_account_journal
msgid "Journal"
msgstr "Diario"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__bank_account_link
msgid "Link to Bank Account"
msgstr "Vinculado a la Cuenta Bancaria"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__name
msgid "Name"
msgstr "Nombre"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Name or Code"
msgstr "Nombre o Código"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__note
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Note"
msgstr "Nota"

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(name)s, the bank account link is 'Fixed' but the fixed "
"bank journal is not set"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(paymode)s, the payment method is %(paymethod)s (it is "
"in fact a debit method), but this debit method is not part of the debit "
"methods of the fixed bank journal %(journal)s"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(paymode)s, the payment method is %(paymethod)s, but "
"this payment method is not part of the payment methods of the fixed bank "
"journal %(journal)s"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Outbound"
msgstr "Saliente"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_method_id
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_form
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Payment Method"
msgstr "Método de pago"

#. module: account_payment_mode
#: model:ir.actions.act_window,name:account_payment_mode.account_payment_method_action
#: model:ir.model,name:account_payment_mode.model_account_payment_method
#: model:ir.ui.menu,name:account_payment_mode.account_payment_method_menu
msgid "Payment Methods"
msgstr "Métodos de Pago"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Payment Mode"
msgstr "Modo de pago"

#. module: account_payment_mode
#: model:ir.actions.act_window,name:account_payment_mode.account_payment_mode_action
#: model:ir.model,name:account_payment_mode.model_account_payment_mode
#: model:ir.ui.menu,name:account_payment_mode.account_payment_mode_menu
msgid "Payment Modes"
msgstr "Modos de Pago"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_type
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Payment Type"
msgstr "Tipo de pago"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__payment_mode_ids
msgid "Payment modes"
msgstr "Modos de pago"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Search Payment Methods"
msgstr "Buscar métodos de pago"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Search Payment Modes"
msgstr "Buscar modos de pago"

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_journal.py:0
#, python-format
msgid ""
"The company of the journal  %(journal)s does not match with the company of "
"the payment mode  %(paymode)s where it is being used in the Allowed Bank "
"Journals."
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_journal.py:0
#, python-format
msgid ""
"The company of the journal %(journal)s does not match with the company of "
"the payment mode %(paymode)s where it is being used as Fixed Bank Journal."
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"The company of the payment mode %(paymode)s, does not match with one of the "
"Allowed Bank Journals."
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_method__code
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_mode__payment_method_code
msgid ""
"This code is used in the code of the Odoo module that handles this payment "
"method. Therefore, if you change it, the generation of the payment file may "
"fail."
msgstr ""
"Este código se usa en el código del módulo de Odoo que gestiona el método de "
"pago. Por lo tanto, si lo cambias, la generación del fichero de pago podría "
"fallar."

#. module: account_payment_mode
#: model:ir.model.fields.selection,name:account_payment_mode.selection__account_payment_mode__bank_account_link__variable
msgid "Variable"
msgstr "Variable"

#~ msgid "A payment method of the same type already exists with this code"
#~ msgstr "Un método de pago del mismo tipo ya existe con este código"

#~ msgid "Inbound Payment Methods"
#~ msgstr "Métodos de Pago Entrantes"

#~ msgid ""
#~ "Manual: Get paid by cash, check or any other method outside of Odoo.\n"
#~ "Electronic: Get paid automatically through a payment acquirer by "
#~ "requesting a transaction on a card saved by the customer when buying or "
#~ "subscribing online (payment token).\n"
#~ "Batch Deposit: Encase several customer checks at once by generating a "
#~ "batch deposit to submit to your bank. When encoding the bank statement in "
#~ "Odoo,you are suggested to reconcile the transaction with the batch "
#~ "deposit. Enable this option from the settings."
#~ msgstr ""
#~ "Manual: Recibe pagos en efectivo, cheque o cualquier otro método fuera de "
#~ "Odoo.\n"
#~ "Electrónico: Recibe pagos automáticamente a través de un adquirente de "
#~ "pago solicitando una transacción en una tarjeta guardada por el cliente "
#~ "al comprar o suscribirse en línea (token de pago).\n"
#~ "Depósito por lotes: Envuelve varios cheques de clientes a la vez "
#~ "generando un depósito por lotes para enviar a su banco. Al codificar el "
#~ "extracto bancario en Odoo, se le sugiere que concilie la transacción con "
#~ "el depósito por lotes. Habilite esta opción desde la configuración."

#~ msgid ""
#~ "Manual:Pay bill by cash or any other method outside of Odoo.\n"
#~ "Check:Pay bill by check and print it from Odoo.\n"
#~ "SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you "
#~ "submit to your bank. Enable this option from the settings."
#~ msgstr ""
#~ "Manual: Pague la factura en efectivo o cualquier otro método fuera de "
#~ "Odoo.\n"
#~ "Cheque: Pague la factura con cheque e imprímala desde Odoo.\n"
#~ "Transferencia de Crédito SEPA: Pague la factura de un archivo de "
#~ "transferencia de crédito SEPA que envíe a su banco. Habilite esta opción "
#~ "desde la configuración."

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the bank account link is 'Fixed' but the fixed "
#~ "bank journal is not set"
#~ msgstr ""
#~ "En el modo de pago '%s', el enlace a la cuenta bancaria es 'Fijo' pero la "
#~ "cuenta bancaria fija no está establecida"

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the payment method is '%s' (it is in fact a "
#~ "debit method), but this debit method is not part of the debit methods of "
#~ "the fixed bank journal '%s'"
#~ msgstr ""
#~ "En el modo de pago '%s', el método de pago es '%s' (es en realidad un "
#~ "método de adeudo), pero este método de adeudo no es parte de los métodos "
#~ "de adeudo del diario bancario fijo '%s'"

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the payment method is '%s', but this payment "
#~ "method is not part of the payment methods of the fixed bank journal '%s'"
#~ msgstr ""
#~ "En el modo de pago '%s', el método de pago es '%s', pero este método de "
#~ "pago no es parte de los métodos de pago del diario bancario fijo '%s'"

#~ msgid "Outbound Payment Methods"
#~ msgstr "Métodos de Pago Salientes"

#, python-format
#~ msgid ""
#~ "The company of the journal '%s' does not match with the company of the "
#~ "payment mode '%s' where it is being used as Fixed Bank Journal."
#~ msgstr ""
#~ "La compañía del diario %s no corresponde con la compañía del modo de pago "
#~ "'%s' que se usa con el diario fijo de banco."

#, python-format
#~ msgid ""
#~ "The company of the journal '%s' does not match with the company of the "
#~ "payment mode '%s' where it is being used in the Allowed Bank Journals."
#~ msgstr ""
#~ "La compañía del diario %s no corresponde con la compañía del modo de pago "
#~ "'%s' que se usa con los diarios permitidos de banco."

#, python-format
#~ msgid ""
#~ "The company of the payment mode '%s', does not match with one of the "
#~ "Allowed Bank Journals."
#~ msgstr ""
#~ "La compañía de la modalidad de pago '%s', no coincide con uno de los "
#~ "Diarios Bancarios Permitidos."

#~ msgid "Bank Account Type"
#~ msgstr "Tipo de Cuenta Bancaria"

#~ msgid "For Incoming Payments"
#~ msgstr "Para Pagos Entrantes"

#~ msgid "For Outgoing Payments"
#~ msgstr "Para Pagos Salientes"

#~ msgid ""
#~ "The company of the payment mode '%s', does not match with the company of "
#~ "journal '%s'."
#~ msgstr ""
#~ "La compañía del modo de pago '%s' no corresponde con la compañía del "
#~ "diario '%s'."

#~ msgid ""
#~ "The company of the payment mode '%s', does not match with the one of the "
#~ "Allowed Bank Journals."
#~ msgstr ""
#~ "La compañía del modo de pago '%s' no corresponde con la compañía de los "
#~ "diarios de banco permitidos."
