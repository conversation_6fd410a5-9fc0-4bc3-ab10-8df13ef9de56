<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="action_bank_reconcile" model="ir.actions.client">
        <field name="name">Reconciliation on Bank Statements</field>
        <field name="res_model">account.bank.statement.line</field>
        <field name="tag">bank_statement_reconciliation_view</field>
    </record>

    <record id="action_view_account_move_line_reconcile" model="ir.actions.client">
        <field name="name">Reconcile</field>
        <field name="tag">manual_reconciliation_view</field>
        <field name="binding_model_id" ref="account.model_account_move_line" />
        <field name="binding_type">action</field>
        <field name="binding_view_types">list</field>
    </record>

    <record id="action_manual_reconciliation" model="ir.actions.client">
        <field name="name">Reconciliation</field>
        <field name="tag">manual_reconciliation_view</field>
    </record>

    <menuitem
        id="menu_action_manual_reconciliation"
        parent="account.menu_finance_entries_actions"
        action="action_manual_reconciliation"
        sequence="25"
    />
</odoo>
