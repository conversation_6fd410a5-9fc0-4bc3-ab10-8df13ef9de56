from odoo import api, fields, models
from datetime import timedelta

class TreasurySituation(models.Model):
    _name = 'treasury.situation'
    _description = 'Treasury Situation'
    _order = 'sequence'
    
    
    name = fields.Char(string='Name')
    encaissement = fields.Float(string='Encaissement', digits='Product Price', readonly=True)
    decaissement = fields.Float(string='Décaissement', digits='Product Price', readonly=True)
    bank = fields.Char(string='Banque', readonly=True)
    payment_type = fields.Selection([('inbound', 'Encaissement'), ('outbound', 'Décaissement')], string='Type de paiement')
    maturity_date = fields.Date(string='Date de maturité')
    state = fields.Selection([('draft', 'Brouillon'), ('open', 'Ouvert'), ('paid', 'Payé'), ('rejected', 'Rejeté')], string='État')
    holder = fields.Many2one('res.partner', string='Holder')
    solde = fields.Float(string='Solde', store=True)
    is_initial_balance = fields.Boolean(string='Is Initial Balance', default=False)
    account_id = fields.Many2one('account.account', string='Bank Account')
    bank_balance = fields.Float(string='Solde Bancaire', readonly=True)
    sequence = fields.Integer(string='Sequence', default=10)
    statut = fields.Char(string='Statut')
    motif = fields.Char(string='Motif')

    @api.model
    def get_bank_balances(self):
        company_id = self.env.company.id
        domain = [('user_type_id', '=', 3)]  # Bank accounts
        
        # Filter by selected bank accounts if provided in context
        selected_accounts = self.env.context.get('selected_bank_accounts')
        if selected_accounts:
            domain.append(('id', 'in', selected_accounts))
            
        bank_accounts = self.env['account.account'].search(domain)

        # First, delete existing initial balance records
        self.search([('is_initial_balance', '=', True)]).unlink()

        moves = self.env['account.move.line'].search([
            ('account_id', 'in', bank_accounts.ids),
            ('move_id.journal_id.coffre', '=', False),
            ('move_id.state', '=', 'posted'),
            ('move_id.journal_id.type', '=', 'bank'),
            ('move_id.journal_id.temporary_bank_journal', '=', False),
            ('company_id', '=', company_id),
        ])

        # Calculate balance per account
        balances = {}
        for line in moves:
            account = line.account_id
            if account not in balances:
                balances[account] = {
                    'account': account,
                    'balance': 0.0,
                    'name': account.name,
                }
            balances[account]['balance'] += line.debit - line.credit

        # Create initial balance records
        return balances
        

    def Get_payment_Operations(self):
        selected_journals = self.env.context.get('selected_journals', [])
        journals = self.env['account.journal'].browse(selected_journals)
        bank_account_ids = journals.mapped('bank_account_id.id')
        bank_ids=journals.mapped('bank_id.id')
        treasury_obj = self.env['account.treasury']

        # First domain
        domain1 = [
            ('state', 'in', ['in_cash', 'coffre', 'versed']),
            ('bank_target', 'in', bank_account_ids),
            ('company_id', '=', self.env.company.id)
        ]

        # Second domain
        domain2 = [
            ('state', 'in', ['in_cash', 'coffre', 'versed']),
            ('bank_origin', 'in', bank_ids),
            ('bank_target', '=', False),
            ('payment_type', '=', 'outbound'),
            ('company_id', '=', self.env.company.id)
        ]
        domain3 = [
            ('state', '=', 'in_cash'),
            ('journal_id', 'in', selected_journals),
            ('company_id', '=', self.env.company.id)
        ]

        # Perform both searches
        entries1 = treasury_obj.search(domain1, order='maturity_date asc')
        entries2 = treasury_obj.search(domain2, order='maturity_date asc')
        entries3 = treasury_obj.search(domain3, order='maturity_date asc')

        # Merge the results and remove duplicates, then sort by effective maturity date
        days_to_add = int(self.env.context.get('date_matiurity_plus') or 0)
        all_entries = (entries1 | entries2 | entries3)
        # Prepare a list of (entry, effective_maturity_date)
        entries_with_dates = []
        for entry in all_entries:
            if entry.payment_type == 'outbound':
                effective_date = entry.maturity_date
            else:
                effective_date = entry.maturity_date + timedelta(days=days_to_add) if entry.maturity_date else None
            entries_with_dates.append((entry, effective_date))
        # Sort by effective maturity date (None values last)
        entries_with_dates.sort(key=lambda x: (x[1] is None, x[1]))
        treasury_entries = [entry for entry, _ in entries_with_dates]

        bank_balances = self.get_bank_balances()
        solde = 0
        sequence = 1
        
        # Create initial balances first
        for account_data in bank_balances.values():
            solde += account_data['balance']
            self.create({
                'name': f"Solde Actuel - {account_data['name']}",
                'bank': account_data['name'],
                'account_id': account_data['account'].id,
                'is_initial_balance': True,
                'maturity_date': False,
                'encaissement': account_data['balance'],
                'decaissement': 0.0,
                'solde': solde,
                'sequence': sequence,
            })
            sequence += 1

        # Process treasury entries in date order
        for entry in treasury_entries:
            days_to_add = int(self.env.context.get('date_matiurity_plus') or 0)
            maturity_date = ( entry.maturity_date if entry.payment_type == 'outbound' else entry.maturity_date + timedelta(days=days_to_add))
            debit = entry.payment_id.move_id.line_ids.filtered(lambda line: line.debit > 0 and line.account_id.user_type_id.id in (3, 5))
            credit = entry.payment_id.move_id.line_ids.filtered(lambda line: line.credit > 0 and line.account_id.user_type_id.id in (3, 5))
            solde += debit.amount_residual if entry.payment_type == 'inbound' else + credit.amount_residual
            statut = 'encours' if entry.state == 'versed' else ('en caisse' if entry.state == 'in_cash' else entry.state)
            self.create({
                'name': f"Payment - {entry.name}",
                'bank': entry.journal_id.name,
                'account_id': entry.journal_id.default_account_id.id,
                'is_initial_balance': False,
                'maturity_date': maturity_date,
                'payment_type': entry.payment_type,
                'motif':entry.x_motif,
                'encaissement': debit.amount_residual if entry.payment_type == 'inbound' else 0.0,
                'decaissement': credit.amount_residual if entry.payment_type == 'outbound' else 0.0,
                'holder': entry.holder.id,
                'solde': solde,
                'sequence': sequence,
                'statut':statut,
            })
            sequence += 1

        return True

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        """Remove automatic refresh on search_read since we now use wizard"""
        return super(TreasurySituation, self).search_read(domain, fields, offset, limit, order)


class BankJournalAnalysis(models.Model):
    _name = 'bank.journal.analysis'
    _description = 'Bank Journal Analysis'
    _order = 'is_initial_balance desc, maturity_date, sequence'

    name = fields.Char(string='Description', readonly=True)
    journal_id = fields.Many2one('account.journal', string='Bank Journal', readonly=True)
    bank_account_id = fields.Many2one('account.account', string='Bank Account', readonly=True)
    encaissement = fields.Float(string='Encaissement', digits='Product Price', readonly=True)
    decaissement = fields.Float(string='Décaissement', digits='Product Price', readonly=True)
    solde = fields.Float(string='Solde', digits='Product Price', readonly=True)
    payment_type = fields.Selection([('inbound', 'Encaissement'), ('outbound', 'Décaissement')], string='Type de paiement', readonly=True)
    maturity_date = fields.Date(string='Date de maturité', readonly=True)
    holder = fields.Many2one('res.partner', string='Holder', readonly=True)
    is_initial_balance = fields.Boolean(string='Is Initial Balance', default=False, readonly=True)
    sequence = fields.Integer(string='Sequence', default=10, readonly=True)
    statut = fields.Char(string='Statut', readonly=True)
    motif = fields.Char(string='Motif', readonly=True)

    @api.model
    def _auto_load_data_if_needed(self):
        """Auto-load data if table is empty and context requires it"""
        if (self.env.context.get('search_default_load_all_data') and
            not self.env.context.get('_loading_data')):
            # Use a different context to avoid recursion
            existing_count = self.with_context(_loading_data=True).search_count([])
            if existing_count == 0:
                # Auto-populate with all data
                self.get_bank_journal_data()

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        """Override search_read to ensure data is loaded"""
        self._auto_load_data_if_needed()
        return super(BankJournalAnalysis, self).search_read(domain, fields, offset, limit, order)

    @api.model
    def web_search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        """Override web_search_read to ensure data is loaded for web interface"""
        self._auto_load_data_if_needed()
        return super(BankJournalAnalysis, self).web_search_read(domain, fields, offset, limit, order)

    @api.model
    def get_bank_journal_data(self, journal_ids=None, date_from=None, date_to=None, partner_ids=None):
        """Get bank journal data with optional filters"""
        # Clear existing records
        self.search([]).unlink()

        # Get all bank journals if none specified
        if not journal_ids:
            journals = self.env['account.journal'].search([('type', '=', 'bank')])
        else:
            journals = self.env['account.journal'].browse(journal_ids)

        # Get bank accounts from journals
        bank_account_ids = journals.mapped('default_account_id.id')

        # Build domain for filtering
        domain = [
            ('account_id', 'in', bank_account_ids),
            ('move_id.journal_id.type', '=', 'bank'),
            ('move_id.state', '=', 'posted'),
            ('company_id', '=', self.env.company.id),
        ]

        # Apply date filters
        if date_from:
            domain.append(('date', '>=', date_from))
        if date_to:
            domain.append(('date', '<=', date_to))

        # Apply partner filter
        if partner_ids:
            domain.append(('partner_id', 'in', partner_ids))

        # Get account move lines
        moves = self.env['account.move.line'].search(domain)

        # First, create all initial balance records for all journals
        sequence = 1
        all_transactions = []

        # Step 1: Create actual current balance records for all journals
        for journal in journals:
            # Calculate actual current balance (all posted moves up to today)
            actual_balance_domain = [
                ('account_id', '=', journal.default_account_id.id),
                ('move_id.state', '=', 'posted'),
                ('company_id', '=', self.env.company.id),
            ]

            # Apply date filter if specified (up to date_to, or today if no date_to)
            if date_to:
                actual_balance_domain.append(('date', '<=', date_to))

            # Apply partner filter if specified
            if partner_ids:
                actual_balance_domain.append(('partner_id', 'in', partner_ids))

            actual_moves = self.env['account.move.line'].search(actual_balance_domain)
            actual_balance = sum(actual_moves.mapped(lambda l: l.debit - l.credit))

            # Create actual balance record
            self.create({
                'name': f"Solde Actuel - {journal.name}",
                'journal_id': journal.id,
                'bank_account_id': journal.default_account_id.id,
                'encaissement': actual_balance if actual_balance > 0 else 0.0,
                'decaissement': abs(actual_balance) if actual_balance < 0 else 0.0,
                'solde': actual_balance,
                'is_initial_balance': True,
                'sequence': sequence,
                'maturity_date': fields.Date.today(),
            })
            sequence += 1

        # Step 2: Collect all transactions from all journals and sort by maturity date
        all_transactions = []
        for journal in journals:
            journal_moves = moves.filtered(lambda m: m.move_id.journal_id.id == journal.id)

            # Add all moves to the collection with journal info
            for move in journal_moves:
                all_transactions.append({
                    'move': move,
                    'journal': journal,
                })

        # Step 3: Sort all transactions by maturity date
        all_transactions.sort(key=lambda t: t['move'].date)

        # Step 4: Create transaction records in date order
        # Note: Since we show actual balance first, transactions show individual amounts
        # without running balance calculation (actual balance is the current state)

        for transaction in all_transactions:
            move = transaction['move']
            journal = transaction['journal']

            self.create({
                'name': move.name or move.move_id.name,
                'journal_id': journal.id,
                'bank_account_id': journal.default_account_id.id,
                'encaissement': move.debit,
                'decaissement': move.credit,
                'solde': move.debit - move.credit,  # Individual transaction amount
                'payment_type': 'inbound' if move.debit > 0 else 'outbound',
                'maturity_date': move.date,
                'holder': move.partner_id.id if move.partner_id else False,
                'is_initial_balance': False,
                'sequence': sequence,
                'motif': move.name or '',
            })
            sequence += 1

        return True

    @api.model
    def refresh_with_filters(self):
        """Refresh data and recalculate balances based on current filters"""
        # This method can be called from the frontend to refresh data
        # For now, we'll just reload all data
        return self.get_bank_journal_data()

    def refresh_data_button(self):
        """Button method to refresh all data"""
        self.get_bank_journal_data()
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    @api.model
    def recalculate_balances_for_filtered_data(self, records):
        """Recalculate running balances for filtered records"""
        # Group records by journal
        journals = {}
        for record in records:
            if record.journal_id.id not in journals:
                journals[record.journal_id.id] = []
            journals[record.journal_id.id].append(record)

        # Recalculate balances for each journal
        for journal_records in journals.values():
            # Sort by sequence to maintain order
            journal_records = sorted(journal_records, key=lambda r: r.sequence)

            # Find initial balance record
            initial_balance = 0.0
            for record in journal_records:
                if record.is_initial_balance:
                    initial_balance = record.solde
                    break

            # Recalculate running balances
            running_balance = initial_balance
            for record in journal_records:
                if not record.is_initial_balance:
                    amount = record.encaissement - record.decaissement
                    running_balance += amount
                    record.solde = running_balance

        return records

