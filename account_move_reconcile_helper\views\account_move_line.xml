<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2017 ACSONE SA/NV
     License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo>
    <record id="account_move_line_tree_view" model="ir.ui.view">
        <field
            name="name"
        >account.move.line.tree (in account_move_reconcile_helper)</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_tree" />
        <field name="arch" type="xml">
            <field name="matching_number" position="after">
                <button
                    name="open_full_reconcile_view"
                    type="object"
                    title="Reconciled lines"
                    icon="fa-list"
                    attrs="{'invisible': [('matching_number', '=', False)]}"
                />
            </field>
        </field>
    </record>

    <record id="view_move_line_tax_audit_tree" model="ir.ui.view">
        <field name="name">account.move.line.tax.audit.tree</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_tax_audit_tree" />
        <field name="arch" type="xml">
            <field name="move_id" position="after">
                <field name="matching_number" invisible="1" />
            </field>
        </field>
    </record>
</odoo>
