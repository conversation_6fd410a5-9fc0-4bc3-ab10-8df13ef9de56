# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reconciliation_widget
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2021-09-20 12:24+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/reconciliation_widget.py:0
#, python-format
msgid "A reconciliation must involve at least 2 move lines."
msgstr "Un lettrage doit inclure au moins 2 lignes."

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/account_bank_statement.py:0
#, python-format
msgid "A selected move line was already reconciled."
msgstr "Une ligne sélectionnée à déjà été lettrée."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Account"
msgstr "Compte"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_account_reconciliation_widget
msgid "Account Reconciliation widget"
msgstr "Outils de lettrage de compte"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"All invoices and payments have been matched, your accounts' balances are "
"clean."
msgstr "Toutes les factures et les paiements ont été rapprochés."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Amount"
msgstr "Montant"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Acc."
msgstr "Compte ana."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Analytic Tags."
msgstr "Étiquettes Analytiques."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_action.js:0
#, python-format
msgid "Bank Reconciliation"
msgstr "Rapprochement bancaire"

#. module: account_reconciliation_widget
#: model:ir.model.fields,field_description:account_reconciliation_widget.field_res_company__account_bank_reconciliation_start
#: model:ir.model.fields,field_description:account_reconciliation_widget.field_res_config_settings__account_bank_reconciliation_start
msgid "Bank Reconciliation Threshold"
msgstr "Seuil de rapprochement bancaire"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_account_bank_statement
msgid "Bank Statement"
msgstr "Relevé bancaire"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Ligne de relevé bancaire"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check all"
msgstr "Tout"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Check that you have no bank statement lines to"
msgstr "Vérifiez que vous n'avez pas de lignes de relevé bancaire à"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Choose counterpart or Create Write-off"
msgstr "Choisissez une contrepartie ou créez une contre passation"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close"
msgstr "Fermer"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Close statement"
msgstr "Fermer"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Congrats, you're all done!"
msgstr "Félicitation, vous avez terminé !"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid ""
"Congrats, you're all done! You reconciled %s transactions in %s. That's on "
"average %s seconds per transaction."
msgstr ""

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create a counterpart"
msgstr "Créer une contrepartie"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Create model"
msgstr "Créer un modèle"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Customer/Vendor Matching"
msgstr "Client/Fournisseur correspondant"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Date"
msgstr ""

#. module: account_reconciliation_widget
#: model_terms:ir.ui.view,arch_db:account_reconciliation_widget.res_config_settings_view_form
msgid ""
"Date from which you started using bank statements in Odoo. Leave empty if "
"you've been using bank statements in Odoo from the start."
msgstr ""

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Description"
msgstr ""

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Due Date"
msgstr "Date d'échéance"

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/account_move.py:0
#, python-format
msgid "Either pass both debit and credit or none."
msgstr "Lien externe"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "External link"
msgstr "Lien externe"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Filter on account, label, partner, amount,..."
msgstr "Filtrer par compte, libellé, partenaire, montant, ..."

#. module: account_reconciliation_widget
#: model:ir.model.fields,field_description:account_reconciliation_widget.field_account_bank_statement__accounting_date
msgid "Financial Date"
msgstr ""

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "From now on, you may want to:"
msgstr "A partir de maintenant, vous souhaitez :"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Go to bank statement(s)"
msgstr "Aller au relevé bancaire"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Good Job!"
msgstr "Bon travail !"

#. module: account_reconciliation_widget
#: model:ir.model.fields,help:account_reconciliation_widget.field_account_bank_statement__accounting_date
msgid ""
"If set, the accounting entries created during the bank statement "
"reconciliation process will be created at this date.\n"
"This is useful if the accounting period in which the entries should normally "
"be booked is already closed."
msgstr ""
"S'il est défini, les écritures comptables créées lors du processus de "
"rapprochement des relevés bancaires seront créées à cette date.\n"
"Ceci est utile si la période comptable dans laquelle les écritures doivent "
"normalement être comptabilisées est déjà clôturée."

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/account_move.py:0
#, python-format
msgid ""
"It is mandatory to specify an account and a journal to create a write-off."
msgstr ""
"Il est obligatoire de spécifier un compte et un journal pour créer une "
"contrepassation."

#. module: account_reconciliation_widget
#: model_terms:ir.ui.view,arch_db:account_reconciliation_widget.account_journal_dashboard_kanban_view
msgid "Items"
msgstr "Lignes"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#: model:ir.model,name:account_reconciliation_widget.model_account_journal
#, python-format
msgid "Journal"
msgstr ""

#. module: account_reconciliation_widget
#: model:ir.model.fields,field_description:account_reconciliation_widget.field_account_bank_statement_line__move_name
msgid "Journal Entry Name"
msgstr "Pièce comptable"

#. module: account_reconciliation_widget
#: model:ir.model,name:account_reconciliation_widget.model_account_move_line
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/reconciliation_widget.py:0
#, python-format
msgid "Journal Items"
msgstr "Écritures comptables"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_action.js:0
#, python-format
msgid "Journal Items to Reconcile"
msgstr "Écritures comptables à lettrer"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_renderer.js:0
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Label"
msgstr "Libellé"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Last Reconciliation:"
msgstr "Dernier lettrage :"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more"
msgstr "Charger plus"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Load more... ("
msgstr "Chargez plus... ("

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Manual Operations"
msgstr "Opérations manuelles"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Match with entries that are not from receivable/payable accounts"
msgstr ""
"Correspondance avec les écritures qui ne proviennent pas de comptes "
"débiteurs / créditeurs"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Miscellaneous Matching"
msgstr "Correspondances diverses"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Modify models"
msgstr "Modifier les modèles"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "New"
msgstr "Nouveau"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Note"
msgstr ""

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Nothing to do!"
msgstr "Rien à faire !"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_model.js:0
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Open balance"
msgstr "Balance d'ouverture"

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/account_bank_statement.py:0
#, python-format
msgid ""
"Operation not allowed. Since your statement line already received a number "
"(%s), you cannot reconcile it entirely with existing journal entries "
"otherwise it would make a gap in the numbering. You should book an entry and "
"make a regular revert of it in case you want to cancel it."
msgstr ""
"Opération non autorisée. Puisque votre ligne de relevé a déjà reçu un nombre "
"(%s), vous ne pouvez pas la lettrer entièrement avec les écritures de "
"journal existantes, sinon cela créerait un vide dans la numérotation. Vous "
"devez réserver une entrée et en faire une annulation au cas où vous voudriez "
"l'annuler."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Partner"
msgstr "Partenaire"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Pay your"
msgstr "Payer votre"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Presets config"
msgstr "Configuration par défaut"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#: model:ir.actions.client,name:account_reconciliation_widget.action_view_account_move_line_reconcile
#: model_terms:ir.ui.view,arch_db:account_reconciliation_widget.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_reconciliation_widget.view_bank_statement_form
#, python-format
msgid "Reconcile"
msgstr "Lettrer"

#. module: account_reconciliation_widget
#: model:ir.actions.client,name:account_reconciliation_widget.action_manual_reconciliation
#: model:ir.ui.menu,name:account_reconciliation_widget.menu_action_manual_reconciliation
msgid "Reconciliation"
msgstr "Lettrage"

#. module: account_reconciliation_widget
#: model:ir.actions.client,name:account_reconciliation_widget.action_bank_reconcile
msgid "Reconciliation on Bank Statements"
msgstr "Rapprochement bancaire"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Ref"
msgstr "Réf"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Residual"
msgstr "Reste"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Save and New"
msgstr "Sauvegarder et Nouveau"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Select Partner"
msgstr "Sélectionner un partenaire"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Select a partner or choose a counterpart"
msgstr "Sélectionner un partenaire et choisissez une contrepartie"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Settings"
msgstr "Paramètres"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Skip"
msgstr "Passer"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_renderer.js:0
#, python-format
msgid "Some fields are undefined"
msgstr "Certains champs sont incomplets"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Tax Included in Price"
msgstr "Taxes incluses"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Taxes"
msgstr ""

#. module: account_reconciliation_widget
#: model:ir.model.fields,help:account_reconciliation_widget.field_account_bank_statement_line__move_name
msgid ""
"Technical field holding the number given to the journal entry,automatically "
"set when the statement line is reconciled then storedto set the same number "
"again if the line is cancelled,set to draft and re-processed again."
msgstr ""

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "That's on average"
msgstr "C'est en moyenne"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/js/reconciliation/reconciliation_model.js:0
#, python-format
msgid "The amount %s is not a valid partial amount"
msgstr "Le montant %s n'est pas un montant partiel valide"

#. module: account_reconciliation_widget
#: model:ir.model.fields,help:account_reconciliation_widget.field_res_company__account_bank_reconciliation_start
#: model:ir.model.fields,help:account_reconciliation_widget.field_res_config_settings__account_bank_reconciliation_start
msgid ""
"The bank reconciliation widget won't ask to reconcile payments older than "
"this date.\n"
"This is useful if you install accounting after having used invoicing for "
"some time and don't want to reconcile all the past payments with bank "
"statements."
msgstr ""
"Le widget de rapprochement bancaire ne vous demandera pas de rapprocher les "
"paiements antérieurs à cette date.\n"
"Ceci est utile si vous installez la comptabilité après avoir utilisé la "
"facturation pendant un certain temps et que vous ne souhaitez pas rapprocher "
"tous les paiements passés avec les relevés bancaires."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "There is nothing to reconcile."
msgstr "l n'y a rien à rapprocher."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid ""
"This page displays all the bank transactions that are to be reconciled and "
"provides with a neat interface to do so."
msgstr ""
"Cette page affiche toutes les transactions bancaires à réconcilier et "
"fournit une interface soignée pour ce faire."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "This payment is registered but not reconciled."
msgstr "Ce paiement est enregistré mais non rapproché."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To Check"
msgstr "A vérifier"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "To speed up reconciliation, define"
msgstr "Pour accélérer le rapprochement, spécifiez"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Transaction"
msgstr ""

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Validate"
msgstr "Valider"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Verify"
msgstr "Vérifier"

#. module: account_reconciliation_widget
#: code:addons/account_reconciliation_widget/models/account_move.py:0
#, python-format
msgid "Write-Off"
msgstr "Contrepassation"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "Writeoff Date"
msgstr "Date de Contrepassation"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "You reconciled"
msgstr "Vous avez lettré"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "and follow-up customers"
msgstr "et les clients suivants"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "have been reconciled automatically."
msgstr "a été lettré automatiquement."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconcile"
msgstr "Lettrer"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "reconciliation models"
msgstr "modèles de lettrage"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "remaining)"
msgstr "restant(s))"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "seconds per transaction."
msgstr "secondes par transaction."

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "statement lines"
msgstr "lignes"

#. module: account_reconciliation_widget
#: model_terms:ir.ui.view,arch_db:account_reconciliation_widget.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "à vérifier"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "transactions in"
msgstr "transactions en"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unpaid invoices"
msgstr "factures impayées"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "unreconciled entries"
msgstr "lignes non lettrées"

#. module: account_reconciliation_widget
#. openerp-web
#: code:addons/account_reconciliation_widget/static/src/xml/account_reconciliation.xml:0
#, python-format
msgid "vendor bills"
msgstr "factures fournisseurs"

#~ msgid "Accounting Date"
#~ msgstr "Date d'écriture"

#~ msgid "Display Name"
#~ msgstr "Nom affiché"

#~ msgid "Last Modified on"
#~ msgstr "Dernière modification le"

#~ msgid ""
#~ "Technical field holding the number given to the journal entry, "
#~ "automatically set when the statement line is reconciled then stored to "
#~ "set the same number again if the line is cancelled, set to draft and re-"
#~ "processed again."
#~ msgstr ""
#~ "Champ technique contenant le numéro attribué à l'écriture de journal, "
#~ "automatiquement défini lors du rapprochement de la ligne de relevé puis "
#~ "stocké pour définir à nouveau le même numéro si la ligne est annulée, "
#~ "définie sur brouillon et à nouveau traitée."
