<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <data noupdate="0">
        <record id="group_account_payment" model="res.groups">
            <field name="name">Accounting / Payments</field>
            <field
                name="users"
                eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"
            />
            <field name="category_id" ref="base.module_category_usability" />
        </record>
    </data>
    <data noupdate="1">
        <record id="account_payment_order_company_rule" model="ir.rule">
            <field name="name">Payment order multi-company rule</field>
            <field name="model_id" ref="model_account_payment_order" />
            <field
                name="domain_force"
            >['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <record id="account_payment_line_company_rule" model="ir.rule">
            <field name="name">Payment line multi-company rule</field>
            <field name="model_id" ref="model_account_payment_line" />
            <field
                name="domain_force"
            >['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>
