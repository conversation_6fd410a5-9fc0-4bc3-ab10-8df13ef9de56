# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_payment_mode
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-04-14 11:29+0000\n"
"PO-Revision-Date: 2021-04-27 09:47+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: French (https://www.transifex.com/oca/teams/23907/fr/)\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_method__bank_account_required
msgid ""
"Activate this option if this payment method requires you to know the bank "
"account number of your customer or supplier."
msgstr ""
"Activez cette option si ce mode de paiement exige que vous connaissiez le "
"numéro de compte bancaire de votre client ou fournisseur."

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__active
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__active
msgid "Active"
msgstr "Actif"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__variable_journal_ids
msgid "Allowed Bank Journals"
msgstr "Journaux de Banque autorisés"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_form
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Archived"
msgstr "Archivé"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__bank_account_required
msgid "Bank Account Required"
msgstr "Compte bancaire requis"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__code
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_method_code
msgid "Code (Do Not Modify)"
msgstr "Code (ne pas modifier)"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__company_id
msgid "Company"
msgstr "Société"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__create_uid
msgid "Created by"
msgstr "Créée par"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__create_date
msgid "Created on"
msgstr "Créée le"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_ct1
msgid "Credit Transfer to Suppliers"
msgstr "Virement a fournisseur"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_dd1
msgid "Direct Debit of customers"
msgstr "Prélevement client"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_dd2
msgid "Direct Debit of suppliers from La Banque Postale"
msgstr "Prélèvement des fournisseurs de La Banque Postale"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_dd1
msgid "Direct Debit of suppliers from Société Générale"
msgstr "Prélèvement des fournisseurs de la Société Générale"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__display_name
msgid "Display Name"
msgstr "Nom à afficher"

#. module: account_payment_mode
#: model:ir.model.fields.selection,name:account_payment_mode.selection__account_payment_mode__bank_account_link__fixed
msgid "Fixed"
msgstr "Fixe"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__fixed_journal_id
msgid "Fixed Bank Journal"
msgstr "Journal bancaire fixe"

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_mode__bank_account_link
msgid ""
"For payment modes that are always attached to the same bank account of your "
"company (such as wire transfer from customers or SEPA direct debit from "
"suppliers), select 'Fixed'. For payment modes that are not always attached "
"to the same bank account (such as SEPA Direct debit for customers, wire "
"transfer to suppliers), you should select 'Variable', which means that you "
"will select the bank account on the payment order. If your company only has "
"one bank account, you should always select 'Fixed'."
msgstr ""
"Pour les modes de paiement qui sont toujours rattachés au même compte "
"bancaire de votre entreprise (comme le virement des clients ou le "
"prélèvement SEPA des fournisseurs), sélectionnez \"Fixe\". Pour les modes de "
"paiement qui ne sont pas toujours rattachés au même compte bancaire (comme "
"le prélèvement SEPA pour les clients, le virement bancaire pour les "
"fournisseurs), vous devez sélectionner \"Variable\", ce qui signifie que "
"vous choisirez le compte bancaire sur l'ordre de paiement. Si votre "
"entreprise n'a qu'un seul compte bancaire, vous devez toujours sélectionner "
"\"Fixe\"."

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Group By"
msgstr "Regrouper par"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__id
msgid "ID"
msgstr "ID"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Inbound"
msgstr "Entrant"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_ct2
msgid "Inbound Credit Trf La Banque Postale"
msgstr "Virement de La Banque Postale"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_ct1
msgid "Inbound Credit Trf Société Générale"
msgstr "Virement de la Société Générale"

#. module: account_payment_mode
#: model:ir.model,name:account_payment_mode.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__write_uid
msgid "Last Updated by"
msgstr "Dernière modification par"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__write_date
msgid "Last Updated on"
msgstr "Modifié le"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__bank_account_link
msgid "Link to Bank Account"
msgstr "Lien vers le compte de banque"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__name
msgid "Name"
msgstr "Nom"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Name or Code"
msgstr "Nom ou Code"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__note
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Note"
msgstr "Note"

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(name)s, the bank account link is 'Fixed' but the fixed "
"bank journal is not set"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(paymode)s, the payment method is %(paymethod)s (it is "
"in fact a debit method), but this debit method is not part of the debit "
"methods of the fixed bank journal %(journal)s"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(paymode)s, the payment method is %(paymethod)s, but "
"this payment method is not part of the payment methods of the fixed bank "
"journal %(journal)s"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Outbound"
msgstr "Sortant"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_method_id
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_form
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Payment Method"
msgstr "Méthode de règlement"

#. module: account_payment_mode
#: model:ir.actions.act_window,name:account_payment_mode.account_payment_method_action
#: model:ir.model,name:account_payment_mode.model_account_payment_method
#: model:ir.ui.menu,name:account_payment_mode.account_payment_method_menu
msgid "Payment Methods"
msgstr "Méthodes de règlement"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Payment Mode"
msgstr "Mode de paiement"

#. module: account_payment_mode
#: model:ir.actions.act_window,name:account_payment_mode.account_payment_mode_action
#: model:ir.model,name:account_payment_mode.model_account_payment_mode
#: model:ir.ui.menu,name:account_payment_mode.account_payment_mode_menu
msgid "Payment Modes"
msgstr "Mode de paiement"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_type
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Payment Type"
msgstr "Type de règlement"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__payment_mode_ids
msgid "Payment modes"
msgstr "Modes de paiement"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Search Payment Methods"
msgstr "Rechercher des méthodes de paiement"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Search Payment Modes"
msgstr "Rechercher des modes de paiement"

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_journal.py:0
#, python-format
msgid ""
"The company of the journal  %(journal)s does not match with the company of "
"the payment mode  %(paymode)s where it is being used in the Allowed Bank "
"Journals."
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_journal.py:0
#, python-format
msgid ""
"The company of the journal %(journal)s does not match with the company of "
"the payment mode %(paymode)s where it is being used as Fixed Bank Journal."
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"The company of the payment mode %(paymode)s, does not match with one of the "
"Allowed Bank Journals."
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_method__code
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_mode__payment_method_code
msgid ""
"This code is used in the code of the Odoo module that handles this payment "
"method. Therefore, if you change it, the generation of the payment file may "
"fail."
msgstr ""
"Ce code est utilisé dans le code du module Odoo qui gère ce mode de "
"paiement. Par conséquent, si vous le modifiez, la génération du fichier de "
"paiement peut échouer."

#. module: account_payment_mode
#: model:ir.model.fields.selection,name:account_payment_mode.selection__account_payment_mode__bank_account_link__variable
msgid "Variable"
msgstr "Variable"

#~ msgid "A payment method of the same type already exists with this code"
#~ msgstr "Un mode de paiement du même type existe déjà avec ce code"

#~ msgid "Inbound Payment Methods"
#~ msgstr "Méthodes de paiement entrantes"

#~ msgid ""
#~ "Manual: Get paid by cash, check or any other method outside of Odoo.\n"
#~ "Electronic: Get paid automatically through a payment acquirer by "
#~ "requesting a transaction on a card saved by the customer when buying or "
#~ "subscribing online (payment token).\n"
#~ "Batch Deposit: Encase several customer checks at once by generating a "
#~ "batch deposit to submit to your bank. When encoding the bank statement in "
#~ "Odoo,you are suggested to reconcile the transaction with the batch "
#~ "deposit. Enable this option from the settings."
#~ msgstr ""
#~ "Manuel : Soyez payé en espèces, par chèque ou par toute autre méthode en "
#~ "dehors d'Odoo.\n"
#~ "Électronique : Se faire payer automatiquement par un acquéreur de "
#~ "paiement en demandant une transaction sur une carte enregistrée par le "
#~ "client lors d'un achat ou d'un abonnement en ligne (jeton de paiement).\n"
#~ "Dépôt par lots : Encodez plusieurs chèques de clients en même temps en "
#~ "générant un dépôt par lot à soumettre à votre banque. Lorsque vous "
#~ "encodez le relevé bancaire dans Odoo, il vous est suggéré de réconcilier "
#~ "la transaction avec le dépôt par lots. Activez cette option dans les "
#~ "paramètres."

#~ msgid ""
#~ "Manual:Pay bill by cash or any other method outside of Odoo.\n"
#~ "Check:Pay bill by check and print it from Odoo.\n"
#~ "SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you "
#~ "submit to your bank. Enable this option from the settings."
#~ msgstr ""
#~ "Manuel : payer une facture en espèces ou par toute autre méthode en "
#~ "dehors d'Odoo.\n"
#~ "Chèque : payer une facture par chèque et l'imprimer depuis Odoo.\n"
#~ "Virement SEPA : Payer la facture à partir d'un fichier de virement SEPA "
#~ "que vous soumettez à votre banque. Activez cette option dans les "
#~ "paramètres."

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the bank account link is 'Fixed' but the fixed "
#~ "bank journal is not set"
#~ msgstr ""
#~ "Pour le mode de paiement '%s', le lien du compte bancaire est \"Fixe\" "
#~ "mais le journal bancaire fixe n'est pas défini"

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the payment method is '%s' (it is in fact a "
#~ "debit method), but this debit method is not part of the debit methods of "
#~ "the fixed bank journal '%s'"
#~ msgstr ""
#~ "Sur le mode de paiement '%s', le mode de paiement est '%s' (c'est en fait "
#~ "un mode de débit), mais ce mode de débit ne fait pas partie des modes de "
#~ "débit du journal bancaire fixe '%s'"

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the payment method is '%s', but this payment "
#~ "method is not part of the payment methods of the fixed bank journal '%s'"
#~ msgstr ""
#~ "Sur le mode de paiement '%s', le mode de paiement est '%s', mais ce mode "
#~ "de paiement ne fait pas partie des modes de paiement du journal bancaire "
#~ "fixe '%s'"

#~ msgid "Outbound Payment Methods"
#~ msgstr "Méthodes de paiement sortant"

#, python-format
#~ msgid ""
#~ "The company of the journal '%s' does not match with the company of the "
#~ "payment mode '%s' where it is being used as Fixed Bank Journal."
#~ msgstr ""
#~ "La société du journal \"%s\" ne correspond pas à la société du mode de "
#~ "paiement \"%s\" lorsqu'il est utilisé comme journal bancaire fixe."

#, python-format
#~ msgid ""
#~ "The company of the journal '%s' does not match with the company of the "
#~ "payment mode '%s' where it is being used in the Allowed Bank Journals."
#~ msgstr ""
#~ "La société du journal \"%s\" ne correspond pas à la société du mode de "
#~ "paiement \"%s\" lorsqu'il est utilisé dans les journaux bancaires "
#~ "autorisés."

#~ msgid "Partner"
#~ msgstr "Partenaire"
