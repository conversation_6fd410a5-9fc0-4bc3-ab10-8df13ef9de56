# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_sourced_by_line
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-23 01:51+0000\n"
"PO-Revision-Date: 2017-11-23 01:51+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2017\n"
"Language-Team: Croatian (https://www.transifex.com/oca/teams/23907/hr/)\n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: sale_sourced_by_line
#: model:ir.model.fields,field_description:sale_sourced_by_line.field_sale_order__warehouse_id
msgid "Default Warehouse"
msgstr ""

#. module: sale_sourced_by_line
#: model:ir.model.fields,help:sale_sourced_by_line.field_sale_order_line__warehouse_id
msgid ""
"If a source warehouse is selected, it will be used to define the route. "
"Otherwise, it will get the warehouse of the sale order"
msgstr ""

#. module: sale_sourced_by_line
#: model:ir.model.fields,help:sale_sourced_by_line.field_sale_order__warehouse_id
msgid ""
"If no source warehouse is selected on line, this warehouse is used as "
"default. "
msgstr ""

#. module: sale_sourced_by_line
#: model:ir.model,name:sale_sourced_by_line.model_sale_order
msgid "Sales Order"
msgstr "Prodajni nalog"

#. module: sale_sourced_by_line
#: model:ir.model,name:sale_sourced_by_line.model_sale_order_line
msgid "Sales Order Line"
msgstr "Stavka ponude"

#. module: sale_sourced_by_line
#: model:ir.model.fields,field_description:sale_sourced_by_line.field_sale_order_line__warehouse_id
msgid "Source Warehouse"
msgstr ""
