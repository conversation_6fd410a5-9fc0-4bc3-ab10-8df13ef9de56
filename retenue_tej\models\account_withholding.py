from odoo import models, fields, http
from odoo.http import request
import base64
import logging
import math
from decimal import Decimal, ROUND_DOWN

_logger = logging.getLogger(__name__)

class AccountWithholding(models.Model):
    _inherit = 'account.withholding'

    def generate_withholding_tax_xml(self):
        all_certificats_xml = ""
        if not self:
          return
        company_vat = self[0].company_id.vat[:8] if self[0].company_id.vat else ''
        year = self[0].date.strftime('%Y') if self[0].date else ''
        month = self[0].date.strftime('%m') if self[0].date else ''
        partner_vat = self[0].company_id.vat[:8] if self.company_id.vat else ''
        xml_data = f"""<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
  <DeclarationsRS VersionSchema="1.0">
      <Declarant>
          <TypeIdentifiant>1</TypeIdentifiant>
          <Identifiant>{company_vat}</Identifiant>
          <CategorieContribuable>PM</CategorieContribuable>
      </Declarant>
      <ReferenceDeclaration>
          <ActeDepot>1</ActeDepot>
          <AnneeDepot>{year}</AnneeDepot>
          <MoisDepot>{month}</MoisDepot>
      </ReferenceDeclaration>
      <AjouterCertificats>
  """
        # Loop over multiple certificates
        for certif in self:
            montant_total_tva = 0
            montant_total_rs = 0
            montant_total_ht = 0
            montant_total_ttc = 0
            operations_xml = ""

            rate_to_name = {
                  10: 'RS1_000002',
                  3: 'RS2_000002',
                  2.5: 'RS6_000001',
                  1.5: 'RS7_000001',
                  1: 'RS7_000002',
                  0.5: 'RS7_000003',
              }
            _logger.info("2")
            sequence = ""
            if certif.account_withholding_tax_ids:
                sequence = rate_to_name.get(certif.account_withholding_tax_ids[0].rate)        
            # Group by tax_ids
            grouped_invoices = {}
            for invoice in certif.account_invoice_ids:
                for line in invoice.invoice_line_ids:
                    tax_ids = tuple(line.tax_ids.ids)  # Tax IDs as grouping key
                    grouped_invoices.setdefault(tax_ids, []).append(line)

            for tax_ids, lines in grouped_invoices.items():
                sum_price_subtotal = round(sum(line.price_subtotal for line in lines),3)
                if tax_ids and certif.env['account.tax'].browse(tax_ids[0]).amount == 1:
                  sum_price_subtotal += sum_price_subtotal * 0.01
                taux_tva = certif.env['account.tax'].browse(tax_ids[1]).amount if tax_ids and certif.env['account.tax'].browse(tax_ids[0]).amount == 1 else (certif.env['account.tax'].browse(tax_ids[0]).amount if tax_ids else 0)  # Get second tax if first tax amount is 1
                montant_tva = math.floor(sum_price_subtotal * taux_tva / 100 * 1000) / 1000
                _logger.info("sum_price_subtotal: %s", sum_price_subtotal)
                _logger.info("montant_tva: %s",montant_tva)
                montant_ttc = sum_price_subtotal + montant_tva
                ttc = Decimal(str(montant_ttc))
                rate = Decimal(str(certif.account_withholding_tax_ids.rate)) if certif.account_withholding_tax_ids else Decimal('0')
                _logger.info("montant_ttc: %s",montant_ttc)
                _logger.info("taxu: %s",certif.account_withholding_tax_ids.rate)
                montant_rs = (ttc * rate / Decimal('100')).quantize(Decimal('0.001'), rounding=ROUND_DOWN)
                _logger.info("montant_rs:%s",montant_rs)
                
                sum_price_subtotal=int(sum_price_subtotal*1000)
                montant_rs=int(montant_rs*1000)
                montant_tva=int(montant_tva*1000)
                montant_ttc=int(montant_tva+sum_price_subtotal)
                #montant_net_servi = math.floor((montant_ttc - montant_rs) * 1000) / 1000
                montant_net_servi=int(montant_ttc - montant_rs)
                montant_total_tva+=montant_tva
                montant_total_rs+=montant_rs
                montant_total_ht+=sum_price_subtotal
                montant_total_ttc+=montant_ttc
                taux_rs=certif.account_withholding_tax_ids.rate if certif.account_withholding_tax_ids else 0
                # Create Operation XML
                if sum_price_subtotal > 0:
                  operations_xml += f"""<Operation IdTypeOperation="{sequence}">
                      <AnneeFacturation>{self.date.strftime('%Y') if self.date else ''}</AnneeFacturation>
                      <CNPC>0</CNPC>
                      <P_Charge>0</P_Charge>
                      <MontantHT>{sum_price_subtotal}</MontantHT>
                      <TauxRS>{taux_rs:g}</TauxRS>
                      <TauxTVA>{taux_tva:g}</TauxTVA>
                      <MontantTVA>{montant_tva}</MontantTVA>
                      <MontantTTC>{montant_ttc}</MontantTTC>
                      <MontantRS>{montant_rs}</MontantRS>
                      <MontantNetServi>{montant_net_servi}</MontantNetServi>
                  </Operation>
                  """
                if operations_xml == "":
                    montant_total_ht=int(certif.amount_total_rs*1000)
                    montant_total_tva=0
                    montant_total_ttc=int(certif.amount_total_rs*1000)
                    montant_total_rs=math.floor(montant_total_ht*certif.account_withholding_tax_ids.rate/100)
                    montant_total_rs=int(montant_total_rs)
                    operations_xml += f"""<Operation IdTypeOperation="{sequence}">
                        <AnneeFacturation>{certif.date.strftime('%Y') if certif.date else ''}</AnneeFacturation>
                        <CNPC>0</CNPC>
                        <P_Charge>0</P_Charge>
                        <MontantHT>{montant_total_ht}</MontantHT>
                        <TauxRS>{certif.account_withholding_tax_ids.rate if certif.account_withholding_tax_ids else 0:g}</TauxRS>
                        <TauxTVA>{0}</TauxTVA>
                        <MontantTVA>{0}</MontantTVA>
                        <MontantTTC>{montant_total_ht}</MontantTTC>
                        <MontantRS>{montant_total_rs}</MontantRS>
                        <MontantNetServi>{(montant_total_ht-montant_total_rs)}</MontantNetServi>
                    </Operation>
                    """
                # Create Certificat XML
                date_naissance_xml = f"<DateNaissance>{certif.partner_id.date_naissance.strftime('%d/%m/%Y')}</DateNaissance>" if certif.partner_id.company_type == 'person' else ''
                # Generate the XML data
                certificat_xml = f"""
                <Certificat>
                    <Beneficiaire>
                <IdTaxpayer>
                  {'<CIN>' if self.partner_id.company_type == 'person' else '<MatriculeFiscal>'}
                  <TypeIdentifiant>{2 if self.partner_id.company_type == 'person' else 1}</TypeIdentifiant>
                  <Identifiant>{self.partner_id.vat[:8] if self.partner_id.company_type == 'company' else self.partner_id.nci}</Identifiant>
                  {date_naissance_xml}
                  <CategorieContribuable>{'PP' if self.partner_id.company_type == 'person' else 'PM'}</CategorieContribuable>
                  {'</CIN>' if self.partner_id.company_type == 'person' else '</MatriculeFiscal>'}
                </IdTaxpayer>
                <Resident>1</Resident>
                <NometprenonOuRaisonsociale>{self.partner_id.name}</NometprenonOuRaisonsociale>
                <Adresse>{self.partner_id.street or 'Tunisie'}</Adresse>
                <Activite>fournisseur</Activite>
                <InfosContact>
                  <AdresseMail>{self.partner_id.email or '<EMAIL>'}</AdresseMail>
                  <NumTel>{self.partner_id.phone or '20 20 20 20'}</NumTel>
                </InfosContact>
                    </Beneficiaire>
                    <DatePayement>{self.date.strftime('%d/%m/%Y') if self.date else ''}</DatePayement>
                    <Ref_certif_chez_declarant>{self.name}</Ref_certif_chez_declarant>
                    <ListeOperations>{operations_xml}</ListeOperations>
              <TotalPayement>
                <TotalMontantHT>{montant_total_ht}</TotalMontantHT>
                <TotalMontantTVA>{montant_total_tva}</TotalMontantTVA>
                <TotalMontantTTC>{montant_total_ttc}</TotalMontantTTC>
                <TotalMontantRS>{montant_total_rs}</TotalMontantRS>
                <TotalMontantNetServi>{montant_total_ttc - montant_total_rs}</TotalMontantNetServi>
              </TotalPayement>
            </Certificat>

        """
            all_certificats_xml += certificat_xml
        # Convert XML data to a downloadable format
        file_name= partner_vat+'-'+self.date.strftime('%Y')+'-'+self.date.strftime('%m')+'-1.xml'
        _logger.info(f"File name: {file_name}")
        file_content = base64.b64encode(xml_data.encode('utf-8'))

        # Create an attachment and trigger the download
        attachment = self.env['ir.attachment'].create({
            'name': file_name,
            'type': 'binary',
            'datas': file_content,
            'store_fname': file_name,
            'res_model': 'account.withholding',
            'res_id': self.id,
            'mimetype': 'application/xml',
        })

        # Return the action to download the file
       
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'self',
        }