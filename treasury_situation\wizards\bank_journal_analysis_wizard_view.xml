<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_bank_journal_analysis_wizard_form" model="ir.ui.view">
        <field name="name">bank.journal.analysis.wizard.form</field>
        <field name="model">bank.journal.analysis.wizard</field>
        <field name="arch" type="xml">
            <form string="Bank Journal Analysis">
                <group>
                    <group>
                        <field name="journal_ids" widget="many2many_tags"/>
                        <field name="partner_ids" widget="many2many_tags"/>
                    </group>
                    <group>
                        <field name="date_from"/>
                        <field name="date_to"/>
                    </group>
                </group>
                <footer>
                    <button name="action_generate_analysis" string="Generate Analysis" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_bank_journal_analysis_wizard" model="ir.actions.act_window">
        <field name="name">Bank Journal Analysis</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">bank.journal.analysis.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
