<odoo>
    <record id="invoice_reconcile_form" model="ir.ui.view">
        <field name="name">Invoice Reconcile</field>
        <field name="model">invoice.reconcile</field>
        <field name="arch" type="xml">
            <form string="Invoice Reconcile" duplicate="0">
                <field name="state" invisible="1"/>
                <field name="available_payment_method_line_ids" invisible="1"/>
                <header>
                    <button name="action_confirm" string="Confirm" type="object" class="oe_highlight"
                            attrs="{'invisible': [('state', '!=', 'draft')]}" data-hotkey="q"/>
                    <button name="action_cancel" string="Cancel" type="object" class="btn btn-secondary"
                            attrs="{'invisible': [('state', 'not in', ['draft', 'confirm'])]}" data-hotkey="q"/>
                </header>

                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <!-- Invoice stat button -->
                        <button class="oe_stat_button" type="object"
                                attrs="{'invisible': [('payment_type', 'not in', ['send', 'receive'])]}"
                                name="action_get_invoices" icon="fa-tasks">
                            <field string="Invoices/Bills" name="invoice_count" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>

                    <group name="main_group">
                        <group name="group1">
                            <field name="payment_type" widget="radio" attrs="{'readonly': [('state', '!=', 'draft')]}"
                                   options="{'horizontal': True}"/>
                            <field name="partner_id" context="{'default_is_company': True}" string="Customer"
                                   options="{'no_quick_create': True}"
                                   attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <label for="Amount"/>
                            <div name="partial_amount_div" class="o_row">
                                <field name="Amount" attrs="{'readonly': [('state', '!=', 'draft')]}" force_save="1"
                                       widget="monetary"/>
                                <field name="currency_id" options="{'no_create': True, 'no_open': True}" required="1"
                                       attrs="{'invisible': [('state', '!=', 'draft')], 'readonly': [('state', '!=', 'draft')]}"/>
                            </div>
                            <field name="date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>
                        <group name="group2">
                            <field name="journal_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="payment_method_line_id" options="{'no_create': True, 'no_open': True}"
                                   required="1"
                                   attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="is_equal_distribution" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Invoices">
                            <field name="invoice_ids" attrs="{'readonly': [('state', '=', 'confirm')]}">
                                <tree editable="true">
                                    <field name="invoice_id" string="Invoice/Bill Number"
                                           domain="[
               ('move_type', '=', parent.payment_type == 'send' and 'in_invoice' or 'out_invoice'),
               ('partner_id', '=', parent_partner_id),
               ('state', '=', 'posted'),
               ('payment_state', '!=', 'paid')
           ]" required="1"/>
                                    <field name="invoice_partner_display_name"
                                           string="Customer/Vendor"/>

                                    <field name="invoice_total" string="Invoice Total" optional="hide"/>
                                    <field name="amount_residual" string="Remaining Amount"/>
                                    <field name="amount_by_user_percentage" string="Percentage"/>
                                    <field name="amount_by_user" string="Amount"/>
                                    <field name="move_type" invisible="1"/>
                                    <field name="payment_state" invisible="1"/>
                                    <field name="state" invisible="1"/>
                                    <field name="parent_partner_id" invisible="1"/>
                                </tree>
                            </field>
                            <group>
                                <field name="partial_amount" string="Total Partial Amount" readonly="1"/>
                                <field name="total_amount_by_user" string="Total Amount By User" readonly="1"/>
                                <field name="remaining_amount" string="Remaining Amount" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="o_attachment_preview"/>
                <div class="oe_chatter">
                    <field name="message_follower_ids" groups="base.group_user"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="invoice_reconcile_tree" model="ir.ui.view">
        <field name="name">Invoice Reconcile</field>
        <field name="model">invoice.reconcile</field>
        <field name="arch" type="xml">
            <tree edit="false" sample="1" decoration-info="state == 'draft'" decoration-muted="state == 'cancel'"
                  duplicate="0">
                <field name="name"/>
                <field name="date" attrs="{'readonly': [('state', 'in', ['cancel', 'posted'])]}"/>
                <field name="journal_id"/>
                <field name="payment_method_line_id"/>
                <field name="partner_id" string="Customer"/>
                <field name="currency_id" string="Payment Currency" optional="hide"/>
                <field name="partial_amount" widget="monetary" string="Partial Amount" sum="Total"/>
                <field name="state" widget="badge" decoration-info="state == 'draft'"
                       decoration-success="state == 'posted'"/>
            </tree>
        </field>
    </record>

    <record id="action_invoice_reconcile1" model="ir.actions.act_window">
        <field name="name">Partial Payments</field>
        <field name="res_model">invoice.reconcile</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[
            ('payment_type', '=', 'receive')
            ]
        </field>
        <field name="context">{'default_payment_type': 'receive', 'invoice': True}</field>
    </record>


    <record id="action_bills_reconcile1" model="ir.actions.act_window">
        <field name="name">Partial Payments</field>
        <field name="res_model">invoice.reconcile</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[
            ('payment_type', '=', 'send')
            ]
        </field>
        <field name="context">{'default_payment_type': 'send','invoice': False}</field>
    </record>


    <data>
        <!-- Menu for Receivables -->
        <menuitem id="menu_account_invoice_reconcile"
                  parent="account.menu_finance_receivables"
                  action="action_invoice_reconcile1"
                  sequence="125"
                  name="Split payment"
        />
        <!-- Menu for Payables -->
        <menuitem id="menu_account_bills_reconcile"
                  parent="account.menu_finance_payables"
                  action="action_bills_reconcile1"
                  sequence="125"
                  name="Split payment"

        />
    </data>
</odoo>
