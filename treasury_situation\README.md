# Treasury Situation Module

This module provides treasury situation management and bank journal analysis for Odoo.

## Features

### 1. Treasury Situation
- Analyze treasury situation based on selected bank journals
- Calculate balances and payment operations
- View encaissements and décaissements

### 2. Bank Journal Analysis (NEW)
- Analyze all bank journals with filtering capabilities
- Filter by:
  - Specific bank journals
  - Date range (from/to)
  - Partners
- Real-time balance calculations
- Detailed transaction view

## Usage

### Bank Journal Analysis

1. Go to **Accounting > Statistics > Analyse des Journaux Bancaires**
2. In the wizard, configure your filters:
   - **Bank Journals**: Select specific journals or leave empty for all
   - **Date From/To**: Set date range or leave empty for all dates
   - **Partners**: Filter by specific partners or leave empty for all
3. Click **Generate Analysis**
4. View the results with:
   - Initial balances for each journal
   - Individual transactions
   - Running balance calculations
   - Filtering and grouping options

### Available Filters in Analysis View

- **Type Filters**: Encaissements, Décaissements
- **Date Filters**: This Month, Last Month, Custom Date Range
- **Group By**: Bank Journal, Partner, Date, Type

### Code Integration

To programmatically generate analysis with filters:

```python
# Get the analysis model
analysis_model = self.env['bank.journal.analysis']

# Generate analysis with filters
analysis_model.get_bank_journal_data(
    journal_ids=[1, 2, 3],  # Specific journal IDs or None for all
    date_from='2024-01-01',  # Start date or None
    date_to='2024-12-31',    # End date or None
    partner_ids=[10, 20],    # Partner IDs or None for all
)

# Read the results
results = analysis_model.search([])
for record in results:
    print(f"Journal: {record.journal_id.name}")
    print(f"Balance: {record.solde}")
    print(f"Date: {record.maturity_date}")
```

## Technical Details

### Models

- `treasury.situation`: Original treasury situation model
- `bank.journal.analysis`: New bank journal analysis model
- `treasury.situation.wizard`: Treasury situation wizard
- `bank.journal.analysis.wizard`: Bank journal analysis wizard

### Key Methods

- `get_bank_journal_data()`: Main method for generating bank journal analysis
- `Get_payment_Operations()`: Original treasury situation calculation method

## Installation

1. Install the module through Odoo Apps
2. The module depends on: `base`, `account`, `product_cost`
3. Access requires `account.group_account_user` permissions

## Menu Structure

- **Accounting > Statistics**
  - **Situation de Trésorerie**: Original treasury situation
  - **Analyse des Journaux Bancaires**: New bank journal analysis
