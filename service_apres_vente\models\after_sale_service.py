from odoo import fields, models, api, _
class AfterSaleService(models.Model):

    _inherit = 'after.sale.service'

    visite_id = fields.One2many('after.sale.visite', 'service_id', string='Visite')
    suivi_labo_id = fields.One2many('suivi.labo', 'service_id', string='Suivi Labo')
    _sql_constraints = [
        ('unique_partner_id', 'UNIQUE(partner_id)', 'This customer already has an After Sale Service entry.')
    ]
