<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Record Rule for Bank Statement Lines -->
        <record id="bank_statement_line_comp_rule" model="ir.rule">
            <field name="name">Bank Statement Line Multi-Company</field>
            <field name="model_id" ref="account.model_account_bank_statement_line"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="global" eval="True"/>
        </record>
    </data>
</odoo>