<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright 2019 ACSONE SA/NV
     License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl). -->
<odoo>
    <record model="ir.ui.view" id="account_payment_method_form_view">
        <field
            name="name"
        >account.payment.method.form (in account_payment_order)</field>
        <field name="model">account.payment.method</field>
        <field
            name="inherit_id"
            ref="account_payment_mode.account_payment_method_form"
        />
        <field name="arch" type="xml">
            <field name="active" position="before">
                <field name="payment_order_only" />
            </field>
        </field>
    </record>
</odoo>
