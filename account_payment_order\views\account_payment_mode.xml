<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="account_payment_mode_form" model="ir.ui.view">
        <field name="name">account_payment_order.account.payment.mode.form</field>
        <field name="model">account.payment.mode</field>
        <field name="inherit_id" ref="account_payment_mode.account_payment_mode_form" />
        <field name="arch" type="xml">
            <field name="payment_type" position="after">
                <field name="payment_order_ok" />
            </field>
            <group name="main" position="after">
                <group
                    name="payment_order_options"
                    string="Options for Payment Orders"
                    attrs="{'invisible': [('payment_order_ok', '=', False)]}"
                >
                    <field
                        name="no_debit_before_maturity"
                        attrs="{'invisible': ['|', ('payment_type', '!=', 'inbound'), ('payment_order_ok', '!=', True)]}"
                    />
                    <field name="default_date_prefered" />
                    <field name="group_lines" />
                </group>
                <group
                    name="payment_order_create_defaults"
                    string="Select Move Lines to Pay - Default Values"
                    attrs="{'invisible': [('payment_order_ok', '=', False)]}"
                >
                    <field name="default_journal_ids" widget="many2many_tags" />
                    <field name="default_payment_mode" />
                    <field name="default_target_move" widget="radio" />
                    <field name="default_invoice" />
                    <field name="default_date_type" />
                </group>
            </group>
        </field>
    </record>
    <record id="account_payment_mode_tree" model="ir.ui.view">
        <field name="name">account_payment_order.account.payment.mode.tree</field>
        <field name="model">account.payment.mode</field>
        <field name="inherit_id" ref="account_payment_mode.account_payment_mode_tree" />
        <field name="arch" type="xml">
            <field name="payment_type" position="after">
                <field name="payment_order_ok" />
            </field>
        </field>
    </record>
    <record id="account_payment_mode_search" model="ir.ui.view">
        <field name="name">account_payment_order.account.payment.mode.search</field>
        <field name="model">account.payment.mode</field>
        <field
            name="inherit_id"
            ref="account_payment_mode.account_payment_mode_search"
        />
        <field name="arch" type="xml">
            <filter name="outbound" position="after">
                <filter
                    name="payment_order_ok"
                    string="Selectable in Payment Orders"
                    domain="[('payment_order_ok', '=', 1)]"
                />
            </filter>
        </field>
    </record>
</odoo>
