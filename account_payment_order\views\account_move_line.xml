<?xml version="1.0" encoding="utf-8" ?>
<!--
  © 2016 Akretion (https://www.akretion.com/)
  <AUTHOR> <<EMAIL>>
  License AGPL-3.0 or later (https://www.gnu.org/licenses/agpl).
-->
<odoo>
    <record id="view_move_line_form" model="ir.ui.view">
        <field name="name">account_payment_order.move_line_form</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account_payment_partner.view_move_line_form" />
        <field name="arch" type="xml">
            <group name="payments" position="inside">
                <field
                    name="partner_bank_id"
                    domain="[('partner_id', '=', partner_id), '|', ('company_id', '=', company_id), ('company_id', '=', False)]"
                />
            </group>
        </field>
    </record>
    <record id="view_move_line_tree" model="ir.ui.view">
        <field name="name">account_payment_order.add.move_line_tree</field>
        <field name="model">account.move.line</field>
        <field name="inherit_id" ref="account.view_move_line_tree" />
        <field name="mode">primary</field>
        <field name="priority">99</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='amount_currency']" position="after">
                <field name="balance" readonly="1" />
                <field name="amount_residual_currency" readonly="1" />
                <field name="amount_residual" readonly="1" />
            </xpath>
            <xpath expr="//field[@name='debit']" position="replace" />
            <xpath expr="//field[@name='credit']" position="replace" />
            <xpath expr="//field[@name='tax_ids']" position="replace" />
        </field>
    </record>
</odoo>
