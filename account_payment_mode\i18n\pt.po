# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_payment_mode
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 9.0c\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-09-10 16:15+0000\n"
"PO-Revision-Date: 2021-10-25 22:36+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese (https://www.transifex.com/oca/teams/23907/pt/)\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_method__bank_account_required
msgid ""
"Activate this option if this payment method requires you to know the bank "
"account number of your customer or supplier."
msgstr ""
"Ative esta opção de pagamento se este método de pagamento o obriga a "
"conhecer o número de conta bancária do seu cliente ou fornecedor."

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__active
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__active
msgid "Active"
msgstr "Ativo"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__variable_journal_ids
msgid "Allowed Bank Journals"
msgstr "Diários de Banco permitidos"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_form
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Archived"
msgstr "Arquivado"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__bank_account_required
msgid "Bank Account Required"
msgstr "Conta Bancária Requerida"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__code
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_method_code
msgid "Code (Do Not Modify)"
msgstr "Código (Não Modificar)"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__company_id
msgid "Company"
msgstr "Empresa"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__create_date
msgid "Created on"
msgstr "Criado em"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_ct1
msgid "Credit Transfer to Suppliers"
msgstr "Transferência de Crédito para fornecedores"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_dd1
msgid "Direct Debit of customers"
msgstr "Débito Direto de clientes"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_dd2
msgid "Direct Debit of suppliers from La Banque Postale"
msgstr "Débito Direto de fornecedores na La Banque Postale"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_dd1
msgid "Direct Debit of suppliers from Société Générale"
msgstr "Débito Direto de fornecedores da Société Générale"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__display_name
msgid "Display Name"
msgstr "Nome a Exibir"

#. module: account_payment_mode
#: model:ir.model.fields.selection,name:account_payment_mode.selection__account_payment_mode__bank_account_link__fixed
msgid "Fixed"
msgstr "Fixo"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__fixed_journal_id
msgid "Fixed Bank Journal"
msgstr "Diário de Banco Fixo"

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_mode__bank_account_link
msgid ""
"For payment modes that are always attached to the same bank account of your "
"company (such as wire transfer from customers or SEPA direct debit from "
"suppliers), select 'Fixed'. For payment modes that are not always attached "
"to the same bank account (such as SEPA Direct debit for customers, wire "
"transfer to suppliers), you should select 'Variable', which means that you "
"will select the bank account on the payment order. If your company only has "
"one bank account, you should always select 'Fixed'."
msgstr ""
"Para modos de pagamento que estão sempre associados à mesma conta bancária "
"da empresa (como transferência bancária de clientes ou débito direto SEPA de "
"fornecedores), selecione 'Fixo'. Para modos de pagamento que nem sempre "
"estão vinculados à mesma conta bancária (como débito direto SEPA para "
"clientes, transferência bancária para fornecedores), deve selecionar "
"'Variável', o que significa que você selecionará a conta bancária na ordem "
"de pagamento. Se a empresa possuir apenas uma conta bancária, deve sempre "
"selecionar 'Fixo'."

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Group By"
msgstr "Agrupar Por"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__id
msgid "ID"
msgstr "ID"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Inbound"
msgstr "Entrada"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_ct2
msgid "Inbound Credit Trf La Banque Postale"
msgstr "Transferência de Entrada a Crédito de La Banque Postale"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_ct1
msgid "Inbound Credit Trf Société Générale"
msgstr "Transferência de Entrada a Crédito da Société Générale"

#. module: account_payment_mode
#: model:ir.model,name:account_payment_mode.model_account_journal
msgid "Journal"
msgstr "Diário"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode____last_update
msgid "Last Modified on"
msgstr "Última Modificação em"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__write_uid
msgid "Last Updated by"
msgstr "Atualizado pela última vez por"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__write_date
msgid "Last Updated on"
msgstr "Atualizado pela última vez em"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__bank_account_link
msgid "Link to Bank Account"
msgstr "Ligação para Conta Bancária"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__name
msgid "Name"
msgstr "Nome"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Name or Code"
msgstr "Nome ou Código"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__note
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Note"
msgstr "Nota"

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(name)s, the bank account link is 'Fixed' but the fixed "
"bank journal is not set"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(paymode)s, the payment method is %(paymethod)s (it is "
"in fact a debit method), but this debit method is not part of the debit "
"methods of the fixed bank journal %(journal)s"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(paymode)s, the payment method is %(paymethod)s, but "
"this payment method is not part of the payment methods of the fixed bank "
"journal %(journal)s"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Outbound"
msgstr "Saída"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_method_id
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_form
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Payment Method"
msgstr "Método de Pagamento"

#. module: account_payment_mode
#: model:ir.actions.act_window,name:account_payment_mode.account_payment_method_action
#: model:ir.model,name:account_payment_mode.model_account_payment_method
#: model:ir.ui.menu,name:account_payment_mode.account_payment_method_menu
msgid "Payment Methods"
msgstr "Métodos de Pagamento"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Payment Mode"
msgstr "Modo de Pagamento"

#. module: account_payment_mode
#: model:ir.actions.act_window,name:account_payment_mode.account_payment_mode_action
#: model:ir.model,name:account_payment_mode.model_account_payment_mode
#: model:ir.ui.menu,name:account_payment_mode.account_payment_mode_menu
msgid "Payment Modes"
msgstr "Modos de Pagamento"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_type
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Payment Type"
msgstr "Tipo do Pagamento"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__payment_mode_ids
msgid "Payment modes"
msgstr "Modos de Pagamento"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Search Payment Methods"
msgstr "Pesquisar Métodos de Pagamento"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Search Payment Modes"
msgstr "Pesquisar Modos de Pagamento"

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_journal.py:0
#, python-format
msgid ""
"The company of the journal  %(journal)s does not match with the company of "
"the payment mode  %(paymode)s where it is being used in the Allowed Bank "
"Journals."
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_journal.py:0
#, python-format
msgid ""
"The company of the journal %(journal)s does not match with the company of "
"the payment mode %(paymode)s where it is being used as Fixed Bank Journal."
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"The company of the payment mode %(paymode)s, does not match with one of the "
"Allowed Bank Journals."
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_method__code
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_mode__payment_method_code
msgid ""
"This code is used in the code of the Odoo module that handles this payment "
"method. Therefore, if you change it, the generation of the payment file may "
"fail."
msgstr ""
"Este código é usado no código do módulo Odoo que lida com este método de "
"pagamento. Se o alterar, a geração do ficheiro de pagamento pode falhar."

#. module: account_payment_mode
#: model:ir.model.fields.selection,name:account_payment_mode.selection__account_payment_mode__bank_account_link__variable
msgid "Variable"
msgstr "Variável"

#~ msgid "A payment method of the same type already exists with this code"
#~ msgstr "Já existe um método de pagamento do mesmo tipo com este código"

#~ msgid "Inbound Payment Methods"
#~ msgstr "Métodos de Pagamento Inbound"

#~ msgid ""
#~ "Manual: Get paid by cash, check or any other method outside of Odoo.\n"
#~ "Electronic: Get paid automatically through a payment acquirer by "
#~ "requesting a transaction on a card saved by the customer when buying or "
#~ "subscribing online (payment token).\n"
#~ "Batch Deposit: Encase several customer checks at once by generating a "
#~ "batch deposit to submit to your bank. When encoding the bank statement in "
#~ "Odoo,you are suggested to reconcile the transaction with the batch "
#~ "deposit. Enable this option from the settings."
#~ msgstr ""
#~ "Manual: Receba pagamentos em numerário, cheque ou qualquer outro método "
#~ "fora do Odoo.\n"
#~ "Eletrónico: Receba pagamentos automaticamente através de um gestor de "
#~ "transações pedindo uma transação com um cartão registado pelo cliente ao "
#~ "comprar ou subscrevendo online (token de pagamento).\n"
#~ "Depósito em Lote: Junte vários cheques de clientes ao mesmo tempo gerando "
#~ "um depósito em lote e submetendo ao seu banco. Quando codificar o "
#~ "extrato bancário no Odoo, é-lhe sugerido que reconcilie a transação com o "
#~ "depósito em lote. Ative esta opção nas definições."

#~ msgid ""
#~ "Manual:Pay bill by cash or any other method outside of Odoo.\n"
#~ "Check:Pay bill by check and print it from Odoo.\n"
#~ "SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you "
#~ "submit to your bank. Enable this option from the settings."
#~ msgstr ""
#~ "Manual: Pague a fornecedores por numerário ou qualquer outro método fora "
#~ "do Odoo.\n"
#~ "Cheque: Pague a for5necedores por cheque e imprima-o através do Odoo.\n"
#~ "Transferência a Crédito SEPA: Pague uma conta através de um ficheiro "
#~ "de Transferência a Crédito SEPA submetido ao seu banco. Ative esta opção "
#~ "nas definições."

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the bank account link is 'Fixed' but the fixed "
#~ "bank journal is not set"
#~ msgstr ""
#~ "No modo de pagamento '%s', o link da conta bancária é 'Fixo', mas o "
#~ "diário do banco fixo não está definido"

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the payment method is '%s' (it is in fact a "
#~ "debit method), but this debit method is not part of the debit methods of "
#~ "the fixed bank journal '%s'"
#~ msgstr ""
#~ "No modo de pagamento '%s', o método de pagamento é '%s' (na verdade é um "
#~ "método de débito), mas este método de débito não faz parte dos métodos de "
#~ "débito do diário de banco fixo '%s'"

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the payment method is '%s', but this payment "
#~ "method is not part of the payment methods of the fixed bank journal '%s'"
#~ msgstr ""
#~ "No modo de pagamento '%s', o método de pagamento é '%s', mas este método "
#~ "de pagamento não faz parte dos métodos de pagamento do diário de banco "
#~ "fixo '%s'"

#~ msgid "Outbound Payment Methods"
#~ msgstr "Métodos de Pagamentos Outbound"

#, python-format
#~ msgid ""
#~ "The company of the journal '%s' does not match with the company of the "
#~ "payment mode '%s' where it is being used as Fixed Bank Journal."
#~ msgstr ""
#~ "A empresa do diário '%s' não corresponde à empresa do modo de pagamento "
#~ "'%s' que está a ser usada como Diário de Banco Fixo."

#, python-format
#~ msgid ""
#~ "The company of the journal '%s' does not match with the company of the "
#~ "payment mode '%s' where it is being used in the Allowed Bank Journals."
#~ msgstr ""
#~ "A empresa do diário '%s' não corresponde à empresa do modo de pagamento "
#~ "'%s' que está a ser usada nos Diários de Banco Permitidos."

#, python-format
#~ msgid ""
#~ "The company of the payment mode '%s', does not match with one of the "
#~ "Allowed Bank Journals."
#~ msgstr ""
#~ "A empresa do modo de pagamento '%s' não corresponde a uma das dos Diários "
#~ "de Bancos Permitidos."
