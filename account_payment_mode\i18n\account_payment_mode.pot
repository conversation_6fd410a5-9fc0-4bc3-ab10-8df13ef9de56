# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment_mode
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_method__bank_account_required
msgid ""
"Activate this option if this payment method requires you to know the bank "
"account number of your customer or supplier."
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__active
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__active
msgid "Active"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__variable_journal_ids
msgid "Allowed Bank Journals"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_form
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Archived"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__bank_account_required
msgid "Bank Account Required"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__code
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_method_code
msgid "Code (Do Not Modify)"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__company_id
msgid "Company"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__create_uid
msgid "Created by"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__create_date
msgid "Created on"
msgstr ""

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_ct1
msgid "Credit Transfer to Suppliers"
msgstr ""

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_dd1
msgid "Direct Debit of customers"
msgstr ""

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_dd2
msgid "Direct Debit of suppliers from La Banque Postale"
msgstr ""

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_dd1
msgid "Direct Debit of suppliers from Société Générale"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__display_name
msgid "Display Name"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields.selection,name:account_payment_mode.selection__account_payment_mode__bank_account_link__fixed
msgid "Fixed"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__fixed_journal_id
msgid "Fixed Bank Journal"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_mode__bank_account_link
msgid ""
"For payment modes that are always attached to the same bank account of your "
"company (such as wire transfer from customers or SEPA direct debit from "
"suppliers), select 'Fixed'. For payment modes that are not always attached "
"to the same bank account (such as SEPA Direct debit for customers, wire "
"transfer to suppliers), you should select 'Variable', which means that you "
"will select the bank account on the payment order. If your company only has "
"one bank account, you should always select 'Fixed'."
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Group By"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__id
msgid "ID"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Inbound"
msgstr ""

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_ct2
msgid "Inbound Credit Trf La Banque Postale"
msgstr ""

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_ct1
msgid "Inbound Credit Trf Société Générale"
msgstr ""

#. module: account_payment_mode
#: model:ir.model,name:account_payment_mode.model_account_journal
msgid "Journal"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode____last_update
msgid "Last Modified on"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__write_uid
msgid "Last Updated by"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__write_date
msgid "Last Updated on"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__bank_account_link
msgid "Link to Bank Account"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__name
msgid "Name"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Name or Code"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__note
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Note"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(name)s, the bank account link is 'Fixed' but the fixed"
" bank journal is not set"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(paymode)s, the payment method is %(paymethod)s (it is "
"in fact a debit method), but this debit method is not part of the debit "
"methods of the fixed bank journal %(journal)s"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(paymode)s, the payment method is %(paymethod)s, but "
"this payment method is not part of the payment methods of the fixed bank "
"journal %(journal)s"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Outbound"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_method_id
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_form
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Payment Method"
msgstr ""

#. module: account_payment_mode
#: model:ir.actions.act_window,name:account_payment_mode.account_payment_method_action
#: model:ir.model,name:account_payment_mode.model_account_payment_method
#: model:ir.ui.menu,name:account_payment_mode.account_payment_method_menu
msgid "Payment Methods"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Payment Mode"
msgstr ""

#. module: account_payment_mode
#: model:ir.actions.act_window,name:account_payment_mode.account_payment_mode_action
#: model:ir.model,name:account_payment_mode.model_account_payment_mode
#: model:ir.ui.menu,name:account_payment_mode.account_payment_mode_menu
msgid "Payment Modes"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_type
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Payment Type"
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__payment_mode_ids
msgid "Payment modes"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Search Payment Methods"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Search Payment Modes"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_journal.py:0
#, python-format
msgid ""
"The company of the journal  %(journal)s does not match with the company of "
"the payment mode  %(paymode)s where it is being used in the Allowed Bank "
"Journals."
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_journal.py:0
#, python-format
msgid ""
"The company of the journal %(journal)s does not match with the company of "
"the payment mode %(paymode)s where it is being used as Fixed Bank Journal."
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"The company of the payment mode %(paymode)s, does not match with one of the "
"Allowed Bank Journals."
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_method__code
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_mode__payment_method_code
msgid ""
"This code is used in the code of the Odoo module that handles this payment "
"method. Therefore, if you change it, the generation of the payment file may "
"fail."
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields.selection,name:account_payment_mode.selection__account_payment_mode__bank_account_link__variable
msgid "Variable"
msgstr ""
