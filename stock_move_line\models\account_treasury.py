from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class AccountTreasury(models.Model):
    _inherit = 'account.treasury'
    date_versement = fields.Date(string='Date de versement', compute='_compute_date_versement', store=False, readonly=True)

    @api.depends('move_line_id')
    def _compute_date_versement(self):
        for record in self:
            try:
                record.date_versement = False
                if record.move_line_id:
                    matching_number = record.move_line_id.matching_number
                    if matching_number:
                        corresponding_move_line = self.env['account.move.line'].search([
                            ('matching_number', '=', matching_number),
                            ('id', '!=', record.move_line_id.id)
                        ], limit=1)
                        if corresponding_move_line:
                            record.date_versement = corresponding_move_line.date
            except Exception as e:
                _logger.warning(f"Error computing date_versement for treasury {record.id}: {e}")
                record.date_versement = False