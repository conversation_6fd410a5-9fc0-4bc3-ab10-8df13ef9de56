=======================
Account Payment Partner
=======================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:8edaf7b8815dd2361ce450e60b0f4c826e7546ddac0954532f0d37206fd8ea93
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fbank--payment-lightgray.png?logo=github
    :target: https://github.com/OCA/bank-payment/tree/15.0/account_payment_partner
    :alt: OCA/bank-payment
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/bank-payment-15-0/bank-payment-15-0-account_payment_partner
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/bank-payment&target_branch=15.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module adds several fields:

* the *Supplier Payment Mode* and *Customer Payment Mode* on Partners,

* the *Payment Mode* on Invoices.

* the *Show bank account* on Payment Mode.

* the *# of digits for customer bank account* on Payment Mode.

* the *Bank account from journals* on Payment Mode.

* the *Payment mode* on Invoices Analysis.

On a Payment Order, in the wizard *Select Invoices to Pay*, the invoices will
be filtered per Payment Mode.

Allows to print in the invoice to which account number the payment
(via SEPA direct debit) is going to be charged so the customer knows that
information, but there are some customers that don't want that everyone
looking at the invoice sees the full account number (and even GDPR can say a
word about that), so that's the reason behind the several options.

**Table of contents**

.. contents::
   :local:

Usage
=====

You are able to add a payment mode directly on a partner.

This payment mode is automatically associated to the invoice related to the
partner. This default value could be changed in a draft invoice.

When you create a payment order, only invoices related to chosen payment mode
are displayed.

Invoices without any payment mode are displayed too.

Changelog
=========

10.0.1.2.0 (2018-05-24)
~~~~~~~~~~~~~~~~~~~~~~~

* [IMP] Add options to show partner bank account in invoice report
  (`#458 <https://github.com/OCA/bank-payment/issues/458>`_)

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/bank-payment/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/bank-payment/issues/new?body=module:%20account_payment_partner%0Aversion:%2015.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
~~~~~~~

* Akretion
* Tecnativa

Contributors
~~~~~~~~~~~~

* Alexis de Lattre <<EMAIL>>
* Raphaël Valyi
* Stefan Rijnhart (Therp)
* Alexandre Fayolle
* Stéphane Bidoul <<EMAIL>>
* Danimar Ribeiro
* Angel Moya <<EMAIL>>
* `Tecnativa <https://www.tecnativa.com>`_:

  * Pedro M. Baeza
  * Carlos Dauden
  * Víctor Martínez
* `DynApps <https://www.dynapps.be>`_:

  * Raf Ven <<EMAIL>>
* Marçal Isern <<EMAIL>>

Maintainers
~~~~~~~~~~~

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/bank-payment <https://github.com/OCA/bank-payment/tree/15.0/account_payment_partner>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
