# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale_sourced_by_line
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 9.0c\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-02-28 03:43+0000\n"
"PO-Revision-Date: 2017-02-28 03:43+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Portuguese (Brazil) (https://www.transifex.com/oca/"
"teams/23907/pt_BR/)\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: sale_sourced_by_line
#: model:ir.model.fields,field_description:sale_sourced_by_line.field_sale_order__warehouse_id
msgid "Default Warehouse"
msgstr ""

#. module: sale_sourced_by_line
#: model:ir.model.fields,help:sale_sourced_by_line.field_sale_order_line__warehouse_id
msgid ""
"If a source warehouse is selected, it will be used to define the route. "
"Otherwise, it will get the warehouse of the sale order"
msgstr ""

#. module: sale_sourced_by_line
#: model:ir.model.fields,help:sale_sourced_by_line.field_sale_order__warehouse_id
msgid ""
"If no source warehouse is selected on line, this warehouse is used as "
"default. "
msgstr ""

#. module: sale_sourced_by_line
#: model:ir.model,name:sale_sourced_by_line.model_sale_order
msgid "Sales Order"
msgstr "Pedido de Venda"

#. module: sale_sourced_by_line
#: model:ir.model,name:sale_sourced_by_line.model_sale_order_line
msgid "Sales Order Line"
msgstr "Linha Pedido de Venda"

#. module: sale_sourced_by_line
#: model:ir.model.fields,field_description:sale_sourced_by_line.field_sale_order_line__warehouse_id
msgid "Source Warehouse"
msgstr ""
