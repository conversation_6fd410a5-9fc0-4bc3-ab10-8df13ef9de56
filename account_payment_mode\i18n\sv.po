# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment_mode
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2021-11-12 16:36+0000\n"
"Last-Translator: <PERSON> <simon.s<PERSON><EMAIL>>\n"
"Language-Team: none\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.3.2\n"

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_method__bank_account_required
msgid ""
"Activate this option if this payment method requires you to know the bank "
"account number of your customer or supplier."
msgstr ""
"Aktivera det här alternativet om denna betalningsmetod kräver att du måste "
"känna till kundens eller leverantörens bankkontonummer."

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__active
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__active
msgid "Active"
msgstr "Aktiv"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__variable_journal_ids
msgid "Allowed Bank Journals"
msgstr "Tillåtna bankjournaler"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_form
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Archived"
msgstr "Arkiverad"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__bank_account_required
msgid "Bank Account Required"
msgstr "Bankkonto krävs"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__code
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_method_code
msgid "Code (Do Not Modify)"
msgstr "Kod (ändra inte)"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__company_id
msgid "Company"
msgstr "Bolag"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_ct1
msgid "Credit Transfer to Suppliers"
msgstr "Kreditöverföring till leverantörer"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_dd1
msgid "Direct Debit of customers"
msgstr "Autogirering av kunder"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_dd2
msgid "Direct Debit of suppliers from La Banque Postale"
msgstr "Autogirering av leverantörer från La Banque Postale"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_outbound_dd1
msgid "Direct Debit of suppliers from Société Générale"
msgstr "Autogirering av leverantörer från Société Générale"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: account_payment_mode
#: model:ir.model.fields.selection,name:account_payment_mode.selection__account_payment_mode__bank_account_link__fixed
msgid "Fixed"
msgstr "Fast"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__fixed_journal_id
msgid "Fixed Bank Journal"
msgstr "Fast bankjournal"

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_mode__bank_account_link
msgid ""
"For payment modes that are always attached to the same bank account of your "
"company (such as wire transfer from customers or SEPA direct debit from "
"suppliers), select 'Fixed'. For payment modes that are not always attached "
"to the same bank account (such as SEPA Direct debit for customers, wire "
"transfer to suppliers), you should select 'Variable', which means that you "
"will select the bank account on the payment order. If your company only has "
"one bank account, you should always select 'Fixed'."
msgstr ""
"För betalningssätt som alltid är kopplade till samma bankkonto i ditt bolag "
"(t.ex. banköverföring från kunder eller SEPA-autogiro från leverantörer), "
"välj \"Fast\". För betalningssätt som inte alltid är kopplade till samma "
"bankkonto (t.ex. SEPA-direktdebitering för kunder, banköverföring till "
"leverantörer) ska du välja \"Variabel\", vilket innebär att du kommer att "
"välja bankkontot på betalningsordern. Om ditt företag endast har ett "
"bankkonto bör du alltid välja \"Fast\"."

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Group By"
msgstr "Gruppera efter"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__id
msgid "ID"
msgstr "ID"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Inbound"
msgstr "Inkommande"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_ct2
msgid "Inbound Credit Trf La Banque Postale"
msgstr "Inkommande kredit Trf La Banque Postale"

#. module: account_payment_mode
#: model:account.payment.mode,name:account_payment_mode.payment_mode_inbound_ct1
msgid "Inbound Credit Trf Société Générale"
msgstr "Inkommande kredit Trf Société Générale"

#. module: account_payment_mode
#: model:ir.model,name:account_payment_mode.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode____last_update
msgid "Last Modified on"
msgstr "Senast modifierad den"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad den"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__bank_account_link
msgid "Link to Bank Account"
msgstr "Länk till bankkonto"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__name
msgid "Name"
msgstr "Namn"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Name or Code"
msgstr "Namn eller kod"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__note
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Note"
msgstr "Anteckning"

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(name)s, the bank account link is 'Fixed' but the fixed "
"bank journal is not set"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(paymode)s, the payment method is %(paymethod)s (it is "
"in fact a debit method), but this debit method is not part of the debit "
"methods of the fixed bank journal %(journal)s"
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"On the payment mode %(paymode)s, the payment method is %(paymethod)s, but "
"this payment method is not part of the payment methods of the fixed bank "
"journal %(journal)s"
msgstr ""

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Outbound"
msgstr "Utgående"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_method_id
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_form
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Payment Method"
msgstr "Betalningsmetod"

#. module: account_payment_mode
#: model:ir.actions.act_window,name:account_payment_mode.account_payment_method_action
#: model:ir.model,name:account_payment_mode.model_account_payment_method
#: model:ir.ui.menu,name:account_payment_mode.account_payment_method_menu
msgid "Payment Methods"
msgstr "Betalningsmetoder"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_form
msgid "Payment Mode"
msgstr "Betalningssätt"

#. module: account_payment_mode
#: model:ir.actions.act_window,name:account_payment_mode.account_payment_mode_action
#: model:ir.model,name:account_payment_mode.model_account_payment_mode
#: model:ir.ui.menu,name:account_payment_mode.account_payment_mode_menu
msgid "Payment Modes"
msgstr "Betalningssätt"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_mode__payment_type
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Payment Type"
msgstr "Typ av betalning"

#. module: account_payment_mode
#: model:ir.model.fields,field_description:account_payment_mode.field_account_payment_method__payment_mode_ids
msgid "Payment modes"
msgstr "Betalningssätt"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_method_search
msgid "Search Payment Methods"
msgstr "Sök betalningsmetoder"

#. module: account_payment_mode
#: model_terms:ir.ui.view,arch_db:account_payment_mode.account_payment_mode_search
msgid "Search Payment Modes"
msgstr "Sök betalningssätt"

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_journal.py:0
#, python-format
msgid ""
"The company of the journal  %(journal)s does not match with the company of "
"the payment mode  %(paymode)s where it is being used in the Allowed Bank "
"Journals."
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_journal.py:0
#, python-format
msgid ""
"The company of the journal %(journal)s does not match with the company of "
"the payment mode %(paymode)s where it is being used as Fixed Bank Journal."
msgstr ""

#. module: account_payment_mode
#: code:addons/account_payment_mode/models/account_payment_mode.py:0
#, python-format
msgid ""
"The company of the payment mode %(paymode)s, does not match with one of the "
"Allowed Bank Journals."
msgstr ""

#. module: account_payment_mode
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_method__code
#: model:ir.model.fields,help:account_payment_mode.field_account_payment_mode__payment_method_code
msgid ""
"This code is used in the code of the Odoo module that handles this payment "
"method. Therefore, if you change it, the generation of the payment file may "
"fail."
msgstr ""
"Den här koden används i koden för den Odoo-modul som hanterar "
"betalningsmetoden. Om du ändrar den kan genereringen av betalningsfilen "
"därför misslyckas."

#. module: account_payment_mode
#: model:ir.model.fields.selection,name:account_payment_mode.selection__account_payment_mode__bank_account_link__variable
msgid "Variable"
msgstr "Variabel"

#~ msgid "A payment method of the same type already exists with this code"
#~ msgstr "En betalningsmetod av samma typ finns redan med den här koden"

#~ msgid "Inbound Payment Methods"
#~ msgstr "Betalningsmetoder för inkommande betalningar"

#~ msgid ""
#~ "Manual: Get paid by cash, check or any other method outside of Odoo.\n"
#~ "Electronic: Get paid automatically through a payment acquirer by "
#~ "requesting a transaction on a card saved by the customer when buying or "
#~ "subscribing online (payment token).\n"
#~ "Batch Deposit: Encase several customer checks at once by generating a "
#~ "batch deposit to submit to your bank. When encoding the bank statement in "
#~ "Odoo,you are suggested to reconcile the transaction with the batch "
#~ "deposit. Enable this option from the settings."
#~ msgstr ""
#~ "Manuell: Få betalt med kontanter, checkar eller andra metoder utanför "
#~ "Odoo.\n"
#~ "Elektronisk: Få betalt automatiskt via en betalningsförmedlare genom att "
#~ "begära en transaktion på ett kort som kunden sparat när han eller hon "
#~ "köper eller prenumererar online (betalningstoken).\n"
#~ "Batchinbetalning: Inkludera flera kundbetalningar på en gång genom att "
#~ "generera en batchinbetalning som kan lämnas in hos din bank. När du kodar "
#~ "bankutdraget i Odoo föreslås att du stämmer av transaktionen med "
#~ "batchinbetalningen. Aktivera det här alternativet i inställningarna."

#~ msgid ""
#~ "Manual:Pay bill by cash or any other method outside of Odoo.\n"
#~ "Check:Pay bill by check and print it from Odoo.\n"
#~ "SEPA Credit Transfer: Pay bill from a SEPA Credit Transfer file you "
#~ "submit to your bank. Enable this option from the settings."
#~ msgstr ""
#~ "Manuell: Betala räkningar med kontanter eller någon annan metod utanför "
#~ "Odoo.\n"
#~ "Check: Betala räkningar med check och skriv ut den från Odoo.\n"
#~ "SEPA-kreditöverföring: Betala räkningar med en SEPA Credit Transfer-fil "
#~ "som du lämnar in hos din bank. Aktivera det här alternativet i "
#~ "inställningarna."

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the bank account link is 'Fixed' but the fixed "
#~ "bank journal is not set"
#~ msgstr ""
#~ "På betalningssättet '%s', är länken till bankkontot 'Fast' men den fasta "
#~ "bankjournalen är inte inställd"

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the payment method is '%s' (it is in fact a "
#~ "debit method), but this debit method is not part of the debit methods of "
#~ "the fixed bank journal '%s'"
#~ msgstr ""
#~ "På betalningssättet '%s', är betalningsmetoden '%s' (det är i själva "
#~ "verket en debiteringsmetod), men denna debiteringsmetod ingår inte i "
#~ "debiteringsmetoderna för den fasta bankjournalen '%s'"

#, python-format
#~ msgid ""
#~ "On the payment mode '%s', the payment method is '%s', but this payment "
#~ "method is not part of the payment methods of the fixed bank journal '%s'"
#~ msgstr ""
#~ "På betalningssättet '%s', är betalningsmetoden '%s', men denna "
#~ "betalningsmetod ingår inte i betalningsmetoderna för den fasta "
#~ "bankjournalen '%s'"

#~ msgid "Outbound Payment Methods"
#~ msgstr "Betalningsmetoder för utgående betalningar"

#, python-format
#~ msgid ""
#~ "The company of the journal '%s' does not match with the company of the "
#~ "payment mode '%s' where it is being used as Fixed Bank Journal."
#~ msgstr ""
#~ "Bolaget på journalen '%s' matchar inte med bolaget på betalningssättet "
#~ "'%s' där den används som fast bankjournal."

#, python-format
#~ msgid ""
#~ "The company of the journal '%s' does not match with the company of the "
#~ "payment mode '%s' where it is being used in the Allowed Bank Journals."
#~ msgstr ""
#~ "Bolaget på journalen '%s' matchar inte med bolaget på betalningssättet "
#~ "'%s' där det används i de tillåtna bankjournalerna."

#, python-format
#~ msgid ""
#~ "The company of the payment mode '%s', does not match with one of the "
#~ "Allowed Bank Journals."
#~ msgstr ""
#~ "Bolaget på betalningssättet '%s', matchar inte med en av de tillåtna "
#~ "bankjournalerna."
