from odoo import api, fields, models
import logging

_logger = logging.getLogger(__name__)

class AccountTreasury(models.Model):
    _inherit = 'account.treasury'

    encaissement=fields.Float(string='Encaissement', digits='Product Price',  readonly=True,_compute='_compute_encaissement' )
    décaissement=fields.Float(string='Décaissement', digits='Product Price',  readonly=True, _compute='_compute_encaissement' )
    bank = fields.Char(string='Banque', readonly=True, _compute='_compute_bank')

    @api.depends('amount','payment_type')
    def _compute_encaissement(self):
        for record in self:
            if record.payment_type=="inbound":
                record.encaissement = record.amount_residual_currency
                record.x_decaissement = 0
            else:
                record.encaissement = 0
                record.x_decaissement = record.amount_residual_currency
    
    @api.depends('payment_type','bank_target','bank_origin')
    def _compute_bank(self):
        for record in self:
            if record.payment_type == "inbound":
                if record.bank_target:
                    _logger.info('record.bank_target %s', record.bank_target)
                    record.bank = str(record.bank_target.bank_id.name) if record.bank_target.bank_id else ''
                else:
                    record.bank = ''
            else:
                if record.bank_origin:
                    _logger.info('record.bank_origin %s', record.bank_origin)
                    record.bank = str(record.bank_origin.name) if record.bank_origin else ''
                else:
                    record.bank = ''

    def recalculate_old_values(self):
        for record in self.search([]):  # Iterate through all records
            record._compute_encaissement()  # Recompute encaissement and décaissement
            record._compute_bank()  # Recompute bank
