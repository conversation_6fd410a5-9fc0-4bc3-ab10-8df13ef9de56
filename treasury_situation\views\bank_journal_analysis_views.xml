<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Search View -->
        <record id="view_bank_journal_analysis_search" model="ir.ui.view">
            <field name="name">bank.journal.analysis.search</field>
            <field name="model">bank.journal.analysis</field>
            <field name="arch" type="xml">
                <search>
                    <!-- Search Fields -->
                    <field name="name" string="Description"/>
                    <field name="journal_id" string="Bank Journal"/>
                    <field name="holder" string="Partner"/>
                    <field name="maturity_date" string="Date"/>
                    <field name="payment_type" string="Type"/>
                    
                    <!-- Filters -->
                    <filter string="Encaissements" name="encaissements" domain="[('payment_type', '=', 'inbound')]"/>
                    <filter string="Décaissements" name="decaissements" domain="[('payment_type', '=', 'outbound')]"/>
                    <separator/>
                    <filter string="Initial Balances" name="initial_balances" domain="[('is_initial_balance', '=', True)]"/>
                    <filter string="Transactions" name="transactions" domain="[('is_initial_balance', '=', False)]"/>
                    <separator/>
                    <filter string="This Month" name="this_month" 
                            domain="[('maturity_date', '>=', (context_today() - relativedelta(day=1)).strftime('%Y-%m-%d')),
                                     ('maturity_date', '&lt;', (context_today() + relativedelta(months=1, day=1)).strftime('%Y-%m-%d'))]"/>
                    <filter string="Last Month" name="last_month"
                            domain="[('maturity_date', '>=', (context_today() - relativedelta(months=1, day=1)).strftime('%Y-%m-%d')),
                                     ('maturity_date', '&lt;', (context_today() - relativedelta(day=1)).strftime('%Y-%m-%d'))]"/>
                    <filter string="Date Filter" name="date_filter" domain="[]"/>
                    
                    <!-- Group By -->
                    <group expand="0" string="Group By">
                        <filter string="Bank Journal" name="group_by_journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                        <filter string="Partner" name="group_by_partner" domain="[]" context="{'group_by': 'holder'}"/>
                        <filter string="Date" name="group_by_date" domain="[]" context="{'group_by': 'maturity_date'}"/>
                        <filter string="Type" name="group_by_type" domain="[]" context="{'group_by': 'payment_type'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Tree View -->
        <record id="view_bank_journal_analysis_tree" model="ir.ui.view">
            <field name="name">bank.journal.analysis.tree</field>
            <field name="model">bank.journal.analysis</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="maturity_date"/>
                    <field name="name"/>
                    <field name="journal_id"/>
                    <field name="holder"/>
                    <field name="encaissement" sum="Total Encaissements"/>
                    <field name="decaissement" sum="Total Décaissements"/>
                    <field name="solde"/>
                    <field name="statut"/>
                    <field name="motif"/>
                    <field name="is_initial_balance" invisible="1"/>
                    <field name="payment_type" invisible="1"/>
                </tree>
            </field>
        </record>

        <!-- Form View -->
        <record id="view_bank_journal_analysis_form" model="ir.ui.view">
            <field name="name">bank.journal.analysis.form</field>
            <field name="model">bank.journal.analysis</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="name"/>
                                <field name="journal_id"/>
                                <field name="bank_account_id"/>
                                <field name="holder"/>
                                <field name="maturity_date"/>
                            </group>
                            <group>
                                <field name="encaissement"/>
                                <field name="decaissement"/>
                                <field name="solde"/>
                                <field name="payment_type"/>
                                <field name="statut"/>
                                <field name="motif"/>
                                <field name="is_initial_balance" invisible="1"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Action -->
        <record id="action_bank_journal_analysis" model="ir.actions.act_window">
            <field name="name">Bank Journal Analysis</field>
            <field name="res_model">bank.journal.analysis</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_bank_journal_analysis_search"/>
            <field name="limit">3000</field>
        </record>

        <!-- Menu Item -->
        <menuitem id="menu_bank_journal_analysis"
                  name="Analyse des Journaux Bancaires"
                  action="action_bank_journal_analysis"
                  parent="product_cost.menu_statistique"
                  groups="account.group_account_user"
                  sequence="21"/>

    </data>
</odoo>
