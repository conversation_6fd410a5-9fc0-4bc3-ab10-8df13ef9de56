from odoo import api, fields, models
import logging

_logger = logging.getLogger(__name__)


class BankJournalAnalysisWizard(models.TransientModel):
    _name = 'bank.journal.analysis.wizard'
    _description = 'Bank Journal Analysis Wizard'

    journal_ids = fields.Many2many(
        'account.journal',
        string='Bank Journals',
        domain=[('type', '=', 'bank')],
        help='Select specific bank journals to analyze. Leave empty to include all bank journals.'
    )
    
    date_from = fields.Date(
        string='Date From',
        help='Start date for the analysis. Leave empty to include all historical data.'
    )
    
    date_to = fields.Date(
        string='Date To',
        help='End date for the analysis. Leave empty to include all data up to today.'
    )
    
    partner_ids = fields.Many2many(
        'res.partner',
        string='Partners',
        help='Filter by specific partners. Leave empty to include all partners.'
    )
    
    def action_generate_analysis(self):
        """Generate bank journal analysis with filters"""
        self.ensure_one()
        
        # Get the bank journal analysis model
        analysis_model = self.env['bank.journal.analysis']
        
        # Generate analysis with filters
        analysis_model.get_bank_journal_data(
            journal_ids=self.journal_ids.ids if self.journal_ids else None,
            date_from=self.date_from,
            date_to=self.date_to,
            partner_ids=self.partner_ids.ids if self.partner_ids else None,
        )
        _logger.info(f'Generated bank journal analysis with filters: {self.journal_ids.ids if self.journal_ids else "All"}')
        
        # Return action to view the analysis
        action = self.env.ref('treasury_situation.action_bank_journal_analysis').read()[0]
        
        # Add context with filter information for display
        context = {
            'search_default_group_by_journal': 1,  # Group by journal by default
        }
        
        # Add date filter to context if specified
        if self.date_from or self.date_to:
            context['search_default_date_filter'] = 1
            
        action['context'] = context
        return action
